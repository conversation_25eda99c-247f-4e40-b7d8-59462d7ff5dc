import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu, X, ShoppingBag, Heart, User } from 'lucide-react';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const navItems = [
    { href: '#collections', label: 'Collections' },
    { href: '#about', label: 'About' },
    { href: '#showcase', label: 'Featured' },
    { href: '#contact', label: 'Contact' },
  ];

  return (
    <motion.header
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.8, delay: 0.2 }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
        isScrolled
          ? 'bg-white/90 backdrop-blur-xl border-b border-luxury-200/30 shadow-luxury-lg'
          : 'bg-black/40 backdrop-blur-xl border-b border-white/20'
      }`}
    >
      {/* Glassmorphism overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-luxury-600/20 via-luxury-500/10 to-rosegold-500/20" />

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-32">

          {/* Logo */}
          <motion.div
            className="flex-shrink-0"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.3 }}
          >
            <a href="#" className="block">
              <div className="relative">
                {/* Luxury background glow */}
                <div className="absolute inset-0 bg-gradient-to-r from-luxury-500/20 to-rosegold-500/20 rounded-2xl blur-xl scale-110" />

                {/* Logo container with glassmorphism */}
                <div className="relative glass-effect rounded-3xl p-6 shadow-luxury-lg">
                  <img
                    src="/images/logo.png"
                    alt="Dollz Boutique"
                    className="h-32 w-auto drop-shadow-2xl transition-all duration-300 hover:drop-shadow-luxury hover:scale-105"
                  />
                </div>

                {/* Floating sparkle animation */}
                <motion.div
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 1, 0.5],
                    rotate: [0, 180, 360]
                  }}
                  transition={{ duration: 4, repeat: Infinity, ease: 'easeInOut' }}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-luxury-400 to-champagne-400 rounded-full opacity-80"
                />
              </div>
            </a>
          </motion.div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navItems.map((item, index) => (
              <motion.a
                key={item.href}
                href={item.href}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                whileHover={{ scale: 1.05, y: -2 }}
                className="relative group"
              >
                <span className={`text-lg font-bold tracking-wide transition-all duration-300 ${
                  isScrolled
                    ? 'text-gray-900 group-hover:text-luxury-600'
                    : 'text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.8)] group-hover:text-luxury-200'
                }`}>
                  {item.label}
                </span>
                <motion.div
                  className="absolute -bottom-1 left-0 h-0.5 bg-gradient-to-r from-luxury-400 to-rosegold-400 rounded-full"
                  initial={{ width: 0 }}
                  whileHover={{ width: '100%' }}
                  transition={{ duration: 0.3 }}
                />
              </motion.a>
            ))}
          </nav>

          {/* Action Buttons */}
          <div className="hidden lg:flex items-center space-x-4">
            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.98 }}
              className={`p-4 rounded-full transition-all duration-300 shadow-luxury ${
                isScrolled
                  ? 'bg-white/80 text-gray-700 hover:bg-white hover:text-luxury-600'
                  : 'bg-white/20 text-white hover:bg-white/30 backdrop-blur-lg'
              }`}
            >
              <Heart className="w-6 h-6" />
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.98 }}
              className={`p-4 rounded-full transition-all duration-300 shadow-luxury ${
                isScrolled
                  ? 'bg-white/80 text-gray-700 hover:bg-white hover:text-luxury-600'
                  : 'bg-white/20 text-white hover:bg-white/30 backdrop-blur-lg'
              }`}
            >
              <User className="w-6 h-6" />
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.98 }}
              className={`p-4 rounded-full transition-all duration-300 shadow-luxury relative ${
                isScrolled
                  ? 'bg-white/80 text-gray-700 hover:bg-white hover:text-luxury-600'
                  : 'bg-white/20 text-white hover:bg-white/30 backdrop-blur-lg'
              }`}
            >
              <ShoppingBag className="w-6 h-6" />
              <span className="absolute -top-1 -right-1 w-5 h-5 bg-luxury-500 text-white text-xs rounded-full flex items-center justify-center font-bold">
                0
              </span>
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.98 }}
              className="ml-6 px-10 py-4 bg-gradient-to-r from-luxury-500 to-luxury-600 text-white rounded-full font-bold text-lg tracking-wide shadow-luxury-lg hover:shadow-luxury hover:from-luxury-600 hover:to-luxury-700 transition-all duration-300"
            >
              Shop Now
            </motion.button>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={toggleMenu}
              className={`p-4 rounded-full transition-all duration-300 shadow-luxury ${
                isScrolled
                  ? 'bg-white/80 text-gray-700 hover:bg-white hover:text-luxury-600'
                  : 'bg-white/20 text-white hover:bg-white/30 backdrop-blur-lg'
              }`}
            >
              <AnimatePresence mode="wait">
                {isMenuOpen ? (
                  <motion.div
                    key="close"
                    initial={{ rotate: -90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: 90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <X className="h-7 w-7" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="menu"
                    initial={{ rotate: 90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: -90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Menu className="h-7 w-7" />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="lg:hidden overflow-hidden"
          >
            <div className="bg-white/10 backdrop-blur-xl border-t border-white/20">
              <div className="px-6 py-8 space-y-6">
                {navItems.map((item, index) => (
                  <motion.a
                    key={item.href}
                    href={item.href}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="block text-white font-semibold text-lg hover:text-luxury-200 transition-colors duration-300 drop-shadow-lg"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.label}
                  </motion.a>
                ))}

                {/* Mobile Action Buttons */}
                <div className="flex items-center justify-center space-x-4 pt-4">
                  <motion.button
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: 0.4 }}
                    className="p-3 glass-effect text-white rounded-full hover:bg-white/20 transition-all duration-300"
                  >
                    <Heart className="w-5 h-5" />
                  </motion.button>

                  <motion.button
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: 0.5 }}
                    className="p-3 glass-effect text-white rounded-full hover:bg-white/20 transition-all duration-300"
                  >
                    <User className="w-5 h-5" />
                  </motion.button>

                  <motion.button
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: 0.6 }}
                    className="p-3 glass-effect text-white rounded-full hover:bg-white/20 transition-all duration-300 relative"
                  >
                    <ShoppingBag className="w-5 h-5" />
                    <span className="absolute -top-1 -right-1 w-4 h-4 bg-luxury-500 text-white text-xs rounded-full flex items-center justify-center font-bold">
                      0
                    </span>
                  </motion.button>
                </div>

                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.7 }}
                  className="w-full mt-6 px-8 py-4 bg-gradient-to-r from-luxury-500 to-luxury-600 text-white rounded-full font-semibold tracking-wide shadow-luxury hover:shadow-luxury-lg transition-all duration-300"
                >
                  Shop Now
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.header>
  );
};

export default Header;
