import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu, X, ShoppingBag, Heart, User } from 'lucide-react';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const navItems = [
    { href: '#collections', label: 'Collections' },
    { href: '#about', label: 'About' },
    { href: '#showcase', label: 'Featured' },
    { href: '#contact', label: 'Contact' },
  ];

  return (
    <motion.header
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.8, delay: 0.2 }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-700 ${
        isScrolled
          ? 'bg-white/95 backdrop-blur-2xl border-b border-gray-200/50 shadow-[0_8px_32px_rgba(0,0,0,0.12)]'
          : 'bg-white/5 backdrop-blur-2xl border-b border-white/10'
      }`}
    >
      {/* Ultra-subtle gradient overlay */}
      <div className={`absolute inset-0 transition-all duration-700 ${
        isScrolled
          ? 'bg-gradient-to-r from-luxury-50/30 via-white/20 to-rosegold-50/30'
          : 'bg-gradient-to-r from-luxury-500/5 via-transparent to-rosegold-500/5'
      }`} />

      <div className="relative max-w-7xl mx-auto px-6 sm:px-8 lg:px-12">
        <div className="flex items-center justify-between h-18">

          {/* Logo */}
          <motion.div
            className="flex-shrink-0"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.4, ease: [0.23, 1, 0.32, 1] }}
          >
            <a href="#" className="block">
              <div className="relative group">
                {/* Minimal luxury container */}
                <div className={`relative transition-all duration-500 ${
                  isScrolled
                    ? 'bg-white/60 backdrop-blur-xl border border-gray-200/30 rounded-xl p-2 shadow-[0_4px_20px_rgba(0,0,0,0.08)]'
                    : 'bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl p-2'
                }`}>
                  <img
                    src="/images/logo.png"
                    alt="Dollz Boutique"
                    className={`h-14 w-auto transition-all duration-500 ${
                      isScrolled
                        ? 'filter brightness-100'
                        : 'filter brightness-110 drop-shadow-[0_2px_8px_rgba(255,255,255,0.3)]'
                    }`}
                  />
                </div>

                {/* Subtle hover glow */}
                <div className="absolute inset-0 bg-gradient-to-r from-luxury-400/0 via-luxury-400/10 to-luxury-400/0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              </div>
            </a>
          </motion.div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-10">
            {navItems.map((item, index) => (
              <motion.a
                key={item.href}
                href={item.href}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                whileHover={{ y: -1 }}
                className="relative group"
              >
                <span className={`text-sm font-medium tracking-[0.5px] transition-all duration-500 ${
                  isScrolled
                    ? 'text-gray-700 group-hover:text-luxury-600'
                    : 'text-white/90 group-hover:text-white'
                }`}>
                  {item.label}
                </span>
                <motion.div
                  className={`absolute -bottom-1 left-0 h-[1px] rounded-full transition-all duration-300 ${
                    isScrolled
                      ? 'bg-gradient-to-r from-luxury-500 to-luxury-600'
                      : 'bg-gradient-to-r from-white/60 to-white/80'
                  }`}
                  initial={{ width: 0, opacity: 0 }}
                  whileHover={{ width: '100%', opacity: 1 }}
                  transition={{ duration: 0.3, ease: [0.23, 1, 0.32, 1] }}
                />
              </motion.a>
            ))}
          </nav>

          {/* Action Buttons */}
          <div className="hidden lg:flex items-center space-x-3">
            <motion.button
              whileHover={{ scale: 1.05, y: -1 }}
              whileTap={{ scale: 0.98 }}
              className={`p-3 rounded-full transition-all duration-500 group ${
                isScrolled
                  ? 'bg-gray-50/80 text-gray-600 hover:bg-white hover:text-luxury-600 border border-gray-200/50'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 hover:text-white border border-white/20'
              }`}
            >
              <Heart className="w-5 h-5 transition-transform duration-300 group-hover:scale-110" />
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.05, y: -1 }}
              whileTap={{ scale: 0.98 }}
              className={`p-3 rounded-full transition-all duration-500 group ${
                isScrolled
                  ? 'bg-gray-50/80 text-gray-600 hover:bg-white hover:text-luxury-600 border border-gray-200/50'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 hover:text-white border border-white/20'
              }`}
            >
              <User className="w-5 h-5 transition-transform duration-300 group-hover:scale-110" />
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.05, y: -1 }}
              whileTap={{ scale: 0.98 }}
              className={`p-3 rounded-full transition-all duration-500 relative group ${
                isScrolled
                  ? 'bg-gray-50/80 text-gray-600 hover:bg-white hover:text-luxury-600 border border-gray-200/50'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 hover:text-white border border-white/20'
              }`}
            >
              <ShoppingBag className="w-5 h-5 transition-transform duration-300 group-hover:scale-110" />
              <span className="absolute -top-1 -right-1 w-4 h-4 bg-luxury-500 text-white text-xs rounded-full flex items-center justify-center font-semibold">
                0
              </span>
            </motion.button>

            <div className="w-px h-6 bg-gray-300/30 mx-2" />

            <motion.button
              whileHover={{ scale: 1.02, y: -1 }}
              whileTap={{ scale: 0.98 }}
              className={`px-6 py-2.5 rounded-full font-medium text-sm tracking-wide transition-all duration-500 ${
                isScrolled
                  ? 'bg-luxury-600 text-white hover:bg-luxury-700 shadow-[0_4px_20px_rgba(219,39,119,0.25)]'
                  : 'bg-white/15 text-white hover:bg-white/25 border border-white/30 backdrop-blur-xl'
              }`}
            >
              Shop Now
            </motion.button>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={toggleMenu}
              className={`p-3 rounded-full transition-all duration-500 ${
                isScrolled
                  ? 'bg-gray-50/80 text-gray-600 hover:bg-white hover:text-luxury-600 border border-gray-200/50'
                  : 'bg-white/10 text-white/80 hover:bg-white/20 border border-white/20'
              }`}
            >
              <AnimatePresence mode="wait">
                {isMenuOpen ? (
                  <motion.div
                    key="close"
                    initial={{ rotate: -90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: 90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <X className="h-5 w-5" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="menu"
                    initial={{ rotate: 90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: -90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Menu className="h-5 w-5" />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="lg:hidden overflow-hidden"
          >
            <div className="bg-white/95 backdrop-blur-2xl border-t border-gray-200/50">
              <div className="px-6 py-6 space-y-4">
                {navItems.map((item, index) => (
                  <motion.a
                    key={item.href}
                    href={item.href}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="block text-gray-700 font-medium text-base hover:text-luxury-600 transition-colors duration-300 py-2"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.label}
                  </motion.a>
                ))}

                {/* Mobile Action Buttons */}
                <div className="flex items-center justify-center space-x-3 pt-4 border-t border-gray-200/50">
                  <motion.button
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: 0.4 }}
                    className="p-3 bg-gray-50 text-gray-600 rounded-full hover:bg-gray-100 hover:text-luxury-600 transition-all duration-300"
                  >
                    <Heart className="w-4 h-4" />
                  </motion.button>

                  <motion.button
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: 0.5 }}
                    className="p-3 bg-gray-50 text-gray-600 rounded-full hover:bg-gray-100 hover:text-luxury-600 transition-all duration-300"
                  >
                    <User className="w-4 h-4" />
                  </motion.button>

                  <motion.button
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: 0.6 }}
                    className="p-3 bg-gray-50 text-gray-600 rounded-full hover:bg-gray-100 hover:text-luxury-600 transition-all duration-300 relative"
                  >
                    <ShoppingBag className="w-4 h-4" />
                    <span className="absolute -top-1 -right-1 w-4 h-4 bg-luxury-500 text-white text-xs rounded-full flex items-center justify-center font-semibold">
                      0
                    </span>
                  </motion.button>
                </div>

                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.7 }}
                  className="w-full mt-4 px-6 py-3 bg-luxury-600 text-white rounded-full font-medium tracking-wide hover:bg-luxury-700 transition-all duration-300"
                >
                  Shop Now
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.header>
  );
};

export default Header;
