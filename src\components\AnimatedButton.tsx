import React from 'react';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';

interface AnimatedButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  icon?: LucideIcon;
  iconPosition?: 'left' | 'right';
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
}

const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  icon: Icon,
  iconPosition = 'right',
  onClick,
  className = '',
  disabled = false,
}) => {
  const baseClasses = 'relative overflow-hidden font-semibold rounded-full transition-all duration-300 flex items-center justify-center';
  
  const variantClasses = {
    primary: 'luxury-button shadow-luxury',
    secondary: 'glass-effect text-gray-800 border border-luxury-200/50 hover:border-luxury-400/50 hover:shadow-luxury',
    ghost: 'text-luxury-600 hover:text-luxury-700 hover:bg-luxury-50',
  };

  const sizeClasses = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
  };

  const buttonClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;

  return (
    <motion.button
      whileHover={{ 
        scale: disabled ? 1 : 1.05, 
        y: disabled ? 0 : -2,
      }}
      whileTap={{ scale: disabled ? 1 : 0.98 }}
      onClick={onClick}
      disabled={disabled}
      className={buttonClasses}
    >
      {/* Shimmer Effect */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
        initial={{ x: '-100%' }}
        whileHover={{ x: '100%' }}
        transition={{ duration: 0.6, ease: 'easeInOut' }}
      />

      {/* Content */}
      <span className="relative z-10 flex items-center">
        {Icon && iconPosition === 'left' && (
          <motion.div
            whileHover={{ x: -2 }}
            transition={{ duration: 0.2 }}
          >
            <Icon className="w-5 h-5 mr-2" />
          </motion.div>
        )}
        
        {children}
        
        {Icon && iconPosition === 'right' && (
          <motion.div
            whileHover={{ x: 2 }}
            transition={{ duration: 0.2 }}
          >
            <Icon className="w-5 h-5 ml-2" />
          </motion.div>
        )}
      </span>
    </motion.button>
  );
};

export default AnimatedButton;
