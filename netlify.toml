[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "*.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[headers]]
  for = "*.mjs"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[headers]]
  for = "*.jsx"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[headers]]
  for = "*.ts"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[headers]]
  for = "*.tsx"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[headers]]
  for = "*.css"
  [headers.values]
    Content-Type = "text/css; charset=utf-8"

[[headers]]
  for = "*.woff"
  [headers.values]
    Content-Type = "font/woff"

[[headers]]
  for = "*.woff2"
  [headers.values]
    Content-Type = "font/woff2"

[[headers]]
  for = "*.ttf"
  [headers.values]
    Content-Type = "font/ttf"

[[headers]]
  for = "*.otf"
  [headers.values]
    Content-Type = "font/otf"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
