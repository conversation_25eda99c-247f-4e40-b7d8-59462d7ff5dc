import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON>, Crown, Gem, Heart } from 'lucide-react';

const FloatingElements: React.FC = () => {
  const elements = [
    { Icon: Sparkles, color: 'text-luxury-400', size: 'w-4 h-4', delay: 0 },
    { Icon: Star, color: 'text-rosegold-400', size: 'w-5 h-5', delay: 1 },
    { Icon: Crown, color: 'text-champagne-400', size: 'w-6 h-6', delay: 2 },
    { Icon: Gem, color: 'text-luxury-300', size: 'w-4 h-4', delay: 3 },
    { Icon: Heart, color: 'text-rosegold-300', size: 'w-5 h-5', delay: 4 },
    { Icon: Sparkles, color: 'text-champagne-300', size: 'w-3 h-3', delay: 5 },
  ];

  return (
    <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">
      {elements.map((element, index) => (
        <motion.div
          key={index}
          className={`absolute ${element.color} ${element.size}`}
          initial={{ 
            opacity: 0,
            scale: 0,
            x: Math.random() * window.innerWidth,
            y: Math.random() * window.innerHeight,
          }}
          animate={{
            opacity: [0, 0.6, 0],
            scale: [0, 1, 0],
            y: [
              Math.random() * window.innerHeight,
              Math.random() * window.innerHeight - 200,
              Math.random() * window.innerHeight - 400,
            ],
            x: [
              Math.random() * window.innerWidth,
              Math.random() * window.innerWidth + (Math.random() - 0.5) * 200,
              Math.random() * window.innerWidth + (Math.random() - 0.5) * 400,
            ],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 8 + Math.random() * 4,
            delay: element.delay,
            repeat: Infinity,
            repeatDelay: 5 + Math.random() * 10,
            ease: 'easeInOut',
          }}
        >
          <element.Icon />
        </motion.div>
      ))}
    </div>
  );
};

export default FloatingElements;
