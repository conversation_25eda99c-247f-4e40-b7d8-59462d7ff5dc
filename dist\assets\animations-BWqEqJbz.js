var gu=Object.defineProperty;var yu=(e,t,i)=>t in e?gu(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i;var C=(e,t,i)=>yu(e,typeof t!="symbol"?t+"":t,i);import{r as Bo,g as _u}from"./vendor-BtP0CW_r.js";var tn={exports:{}},Ye={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sr;function vu(){if(sr)return Ye;sr=1;var e=Bo(),t=Symbol.for("react.element"),i=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,s=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,r={key:!0,ref:!0,__self:!0,__source:!0};function o(a,l,c){var u,h={},f=null,d=null;c!==void 0&&(f=""+c),l.key!==void 0&&(f=""+l.key),l.ref!==void 0&&(d=l.ref);for(u in l)n.call(l,u)&&!r.hasOwnProperty(u)&&(h[u]=l[u]);if(a&&a.defaultProps)for(u in l=a.defaultProps,l)h[u]===void 0&&(h[u]=l[u]);return{$$typeof:t,type:a,key:f,ref:d,props:h,_owner:s.current}}return Ye.Fragment=i,Ye.jsx=o,Ye.jsxs=o,Ye}var rr;function xu(){return rr||(rr=1,tn.exports=vu()),tn.exports}var Bt=xu(),A=Bo();const Rm=_u(A),ss=A.createContext({});function rs(e){const t=A.useRef(null);return t.current===null&&(t.current=e()),t.current}const os=typeof window<"u",No=os?A.useLayoutEffect:A.useEffect,Ki=A.createContext(null);function as(e,t){e.indexOf(t)===-1&&e.push(t)}function ls(e,t){const i=e.indexOf(t);i>-1&&e.splice(i,1)}const Nt=(e,t,i)=>i>t?t:i<e?e:i;let us=()=>{};const zt={},zo=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e);function jo(e){return typeof e=="object"&&e!==null}const Uo=e=>/^0[^.\s]+$/u.test(e);function cs(e){let t;return()=>(t===void 0&&(t=e()),t)}const Pt=e=>e,Tu=(e,t)=>i=>t(e(i)),_i=(...e)=>e.reduce(Tu),si=(e,t,i)=>{const n=t-e;return n===0?1:(i-e)/n};class hs{constructor(){this.subscriptions=[]}add(t){return as(this.subscriptions,t),()=>ls(this.subscriptions,t)}notify(t,i,n){const s=this.subscriptions.length;if(s)if(s===1)this.subscriptions[0](t,i,n);else for(let r=0;r<s;r++){const o=this.subscriptions[r];o&&o(t,i,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Rt=e=>e*1e3,Et=e=>e/1e3;function Wo(e,t){return t?e*(1e3/t):0}const Yo=(e,t,i)=>(((1-3*i+3*t)*e+(3*i-6*t))*e+3*t)*e,wu=1e-7,Su=12;function bu(e,t,i,n,s){let r,o,a=0;do o=t+(i-t)/2,r=Yo(o,n,s)-e,r>0?i=o:t=o;while(Math.abs(r)>wu&&++a<Su);return o}function vi(e,t,i,n){if(e===t&&i===n)return Pt;const s=r=>bu(r,0,1,e,i);return r=>r===0||r===1?r:Yo(s(r),t,n)}const Xo=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,$o=e=>t=>1-e(1-t),Ko=vi(.33,1.53,.69,.99),fs=$o(Ko),Ho=Xo(fs),Go=e=>(e*=2)<1?.5*fs(e):.5*(2-Math.pow(2,-10*(e-1))),ds=e=>1-Math.sin(Math.acos(e)),qo=$o(ds),Zo=Xo(ds),Pu=vi(.42,0,1,1),Au=vi(0,0,.58,1),Qo=vi(.42,0,.58,1),Cu=e=>Array.isArray(e)&&typeof e[0]!="number",Jo=e=>Array.isArray(e)&&typeof e[0]=="number",Mu={linear:Pt,easeIn:Pu,easeInOut:Qo,easeOut:Au,circIn:ds,circInOut:Zo,circOut:qo,backIn:fs,backInOut:Ho,backOut:Ko,anticipate:Go},Du=e=>typeof e=="string",or=e=>{if(Jo(e)){us(e.length===4);const[t,i,n,s]=e;return vi(t,i,n,s)}else if(Du(e))return Mu[e];return e},Si=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ar={value:null};function Ru(e,t){let i=new Set,n=new Set,s=!1,r=!1;const o=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function c(h){o.has(h)&&(u.schedule(h),e()),l++,h(a)}const u={schedule:(h,f=!1,d=!1)=>{const p=d&&s?i:n;return f&&o.add(h),p.has(h)||p.add(h),h},cancel:h=>{n.delete(h),o.delete(h)},process:h=>{if(a=h,s){r=!0;return}s=!0,[i,n]=[n,i],i.forEach(c),t&&ar.value&&ar.value.frameloop[t].push(l),l=0,i.clear(),s=!1,r&&(r=!1,u.process(h))}};return u}const Eu=40;function ta(e,t){let i=!1,n=!0;const s={delta:0,timestamp:0,isProcessing:!1},r=()=>i=!0,o=Si.reduce((v,w)=>(v[w]=Ru(r,t?w:void 0),v),{}),{setup:a,read:l,resolveKeyframes:c,preUpdate:u,update:h,preRender:f,render:d,postRender:m}=o,p=()=>{const v=zt.useManualTiming?s.timestamp:performance.now();i=!1,zt.useManualTiming||(s.delta=n?1e3/60:Math.max(Math.min(v-s.timestamp,Eu),1)),s.timestamp=v,s.isProcessing=!0,a.process(s),l.process(s),c.process(s),u.process(s),h.process(s),f.process(s),d.process(s),m.process(s),s.isProcessing=!1,i&&t&&(n=!1,e(p))},g=()=>{i=!0,n=!0,s.isProcessing||e(p)};return{schedule:Si.reduce((v,w)=>{const _=o[w];return v[w]=(T,b=!1,S=!1)=>(i||g(),_.schedule(T,b,S)),v},{}),cancel:v=>{for(let w=0;w<Si.length;w++)o[Si[w]].cancel(v)},state:s,steps:o}}const{schedule:j,cancel:Zt,state:tt,steps:en}=ta(typeof requestAnimationFrame<"u"?requestAnimationFrame:Pt,!0);let Mi;function Vu(){Mi=void 0}const at={now:()=>(Mi===void 0&&at.set(tt.isProcessing||zt.useManualTiming?tt.timestamp:performance.now()),Mi),set:e=>{Mi=e,queueMicrotask(Vu)}},ea=e=>t=>typeof t=="string"&&t.startsWith(e),ps=ea("--"),ku=ea("var(--"),ms=e=>ku(e)?Ou.test(e.split("/*")[0].trim()):!1,Ou=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ze={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},ri={...ze,transform:e=>Nt(0,1,e)},bi={...ze,default:1},qe=e=>Math.round(e*1e5)/1e5,gs=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Lu(e){return e==null}const Fu=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ys=(e,t)=>i=>!!(typeof i=="string"&&Fu.test(i)&&i.startsWith(e)||t&&!Lu(i)&&Object.prototype.hasOwnProperty.call(i,t)),ia=(e,t,i)=>n=>{if(typeof n!="string")return n;const[s,r,o,a]=n.match(gs);return{[e]:parseFloat(s),[t]:parseFloat(r),[i]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},Iu=e=>Nt(0,255,e),nn={...ze,transform:e=>Math.round(Iu(e))},ce={test:ys("rgb","red"),parse:ia("red","green","blue"),transform:({red:e,green:t,blue:i,alpha:n=1})=>"rgba("+nn.transform(e)+", "+nn.transform(t)+", "+nn.transform(i)+", "+qe(ri.transform(n))+")"};function Bu(e){let t="",i="",n="",s="";return e.length>5?(t=e.substring(1,3),i=e.substring(3,5),n=e.substring(5,7),s=e.substring(7,9)):(t=e.substring(1,2),i=e.substring(2,3),n=e.substring(3,4),s=e.substring(4,5),t+=t,i+=i,n+=n,s+=s),{red:parseInt(t,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:s?parseInt(s,16)/255:1}}const Sn={test:ys("#"),parse:Bu,transform:ce.transform},xi=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Xt=xi("deg"),Vt=xi("%"),M=xi("px"),Nu=xi("vh"),zu=xi("vw"),lr={...Vt,parse:e=>Vt.parse(e)/100,transform:e=>Vt.transform(e*100)},we={test:ys("hsl","hue"),parse:ia("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:i,alpha:n=1})=>"hsla("+Math.round(e)+", "+Vt.transform(qe(t))+", "+Vt.transform(qe(i))+", "+qe(ri.transform(n))+")"},G={test:e=>ce.test(e)||Sn.test(e)||we.test(e),parse:e=>ce.test(e)?ce.parse(e):we.test(e)?we.parse(e):Sn.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?ce.transform(e):we.transform(e),getAnimatableNone:e=>{const t=G.parse(e);return t.alpha=0,G.transform(t)}},ju=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Uu(e){var t,i;return isNaN(e)&&typeof e=="string"&&(((t=e.match(gs))==null?void 0:t.length)||0)+(((i=e.match(ju))==null?void 0:i.length)||0)>0}const na="number",sa="color",Wu="var",Yu="var(",ur="${}",Xu=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function oi(e){const t=e.toString(),i=[],n={color:[],number:[],var:[]},s=[];let r=0;const a=t.replace(Xu,l=>(G.test(l)?(n.color.push(r),s.push(sa),i.push(G.parse(l))):l.startsWith(Yu)?(n.var.push(r),s.push(Wu),i.push(l)):(n.number.push(r),s.push(na),i.push(parseFloat(l))),++r,ur)).split(ur);return{values:i,split:a,indexes:n,types:s}}function ra(e){return oi(e).values}function oa(e){const{split:t,types:i}=oi(e),n=t.length;return s=>{let r="";for(let o=0;o<n;o++)if(r+=t[o],s[o]!==void 0){const a=i[o];a===na?r+=qe(s[o]):a===sa?r+=G.transform(s[o]):r+=s[o]}return r}}const $u=e=>typeof e=="number"?0:G.test(e)?G.getAnimatableNone(e):e;function Ku(e){const t=ra(e);return oa(e)(t.map($u))}const Qt={test:Uu,parse:ra,createTransformer:oa,getAnimatableNone:Ku};function sn(e,t,i){return i<0&&(i+=1),i>1&&(i-=1),i<1/6?e+(t-e)*6*i:i<1/2?t:i<2/3?e+(t-e)*(2/3-i)*6:e}function Hu({hue:e,saturation:t,lightness:i,alpha:n}){e/=360,t/=100,i/=100;let s=0,r=0,o=0;if(!t)s=r=o=i;else{const a=i<.5?i*(1+t):i+t-i*t,l=2*i-a;s=sn(l,a,e+1/3),r=sn(l,a,e),o=sn(l,a,e-1/3)}return{red:Math.round(s*255),green:Math.round(r*255),blue:Math.round(o*255),alpha:n}}function Li(e,t){return i=>i>0?t:e}const N=(e,t,i)=>e+(t-e)*i,rn=(e,t,i)=>{const n=e*e,s=i*(t*t-n)+n;return s<0?0:Math.sqrt(s)},Gu=[Sn,ce,we],qu=e=>Gu.find(t=>t.test(e));function cr(e){const t=qu(e);if(!t)return!1;let i=t.parse(e);return t===we&&(i=Hu(i)),i}const hr=(e,t)=>{const i=cr(e),n=cr(t);if(!i||!n)return Li(e,t);const s={...i};return r=>(s.red=rn(i.red,n.red,r),s.green=rn(i.green,n.green,r),s.blue=rn(i.blue,n.blue,r),s.alpha=N(i.alpha,n.alpha,r),ce.transform(s))},bn=new Set(["none","hidden"]);function Zu(e,t){return bn.has(e)?i=>i<=0?e:t:i=>i>=1?t:e}function Qu(e,t){return i=>N(e,t,i)}function _s(e){return typeof e=="number"?Qu:typeof e=="string"?ms(e)?Li:G.test(e)?hr:ec:Array.isArray(e)?aa:typeof e=="object"?G.test(e)?hr:Ju:Li}function aa(e,t){const i=[...e],n=i.length,s=e.map((r,o)=>_s(r)(r,t[o]));return r=>{for(let o=0;o<n;o++)i[o]=s[o](r);return i}}function Ju(e,t){const i={...e,...t},n={};for(const s in i)e[s]!==void 0&&t[s]!==void 0&&(n[s]=_s(e[s])(e[s],t[s]));return s=>{for(const r in n)i[r]=n[r](s);return i}}function tc(e,t){const i=[],n={color:0,var:0,number:0};for(let s=0;s<t.values.length;s++){const r=t.types[s],o=e.indexes[r][n[r]],a=e.values[o]??0;i[s]=a,n[r]++}return i}const ec=(e,t)=>{const i=Qt.createTransformer(t),n=oi(e),s=oi(t);return n.indexes.var.length===s.indexes.var.length&&n.indexes.color.length===s.indexes.color.length&&n.indexes.number.length>=s.indexes.number.length?bn.has(e)&&!s.values.length||bn.has(t)&&!n.values.length?Zu(e,t):_i(aa(tc(n,s),s.values),i):Li(e,t)};function la(e,t,i){return typeof e=="number"&&typeof t=="number"&&typeof i=="number"?N(e,t,i):_s(e)(e,t)}const ic=e=>{const t=({timestamp:i})=>e(i);return{start:(i=!0)=>j.update(t,i),stop:()=>Zt(t),now:()=>tt.isProcessing?tt.timestamp:at.now()}},ua=(e,t,i=10)=>{let n="";const s=Math.max(Math.round(t/i),2);for(let r=0;r<s;r++)n+=Math.round(e(r/(s-1))*1e4)/1e4+", ";return`linear(${n.substring(0,n.length-2)})`},Fi=2e4;function vs(e){let t=0;const i=50;let n=e.next(t);for(;!n.done&&t<Fi;)t+=i,n=e.next(t);return t>=Fi?1/0:t}function nc(e,t=100,i){const n=i({...e,keyframes:[0,t]}),s=Math.min(vs(n),Fi);return{type:"keyframes",ease:r=>n.next(s*r).value/t,duration:Et(s)}}const sc=5;function ca(e,t,i){const n=Math.max(t-sc,0);return Wo(i-e(n),t-n)}const U={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},on=.001;function rc({duration:e=U.duration,bounce:t=U.bounce,velocity:i=U.velocity,mass:n=U.mass}){let s,r,o=1-t;o=Nt(U.minDamping,U.maxDamping,o),e=Nt(U.minDuration,U.maxDuration,Et(e)),o<1?(s=c=>{const u=c*o,h=u*e,f=u-i,d=Pn(c,o),m=Math.exp(-h);return on-f/d*m},r=c=>{const h=c*o*e,f=h*i+i,d=Math.pow(o,2)*Math.pow(c,2)*e,m=Math.exp(-h),p=Pn(Math.pow(c,2),o);return(-s(c)+on>0?-1:1)*((f-d)*m)/p}):(s=c=>{const u=Math.exp(-c*e),h=(c-i)*e+1;return-on+u*h},r=c=>{const u=Math.exp(-c*e),h=(i-c)*(e*e);return u*h});const a=5/e,l=ac(s,r,a);if(e=Rt(e),isNaN(l))return{stiffness:U.stiffness,damping:U.damping,duration:e};{const c=Math.pow(l,2)*n;return{stiffness:c,damping:o*2*Math.sqrt(n*c),duration:e}}}const oc=12;function ac(e,t,i){let n=i;for(let s=1;s<oc;s++)n=n-e(n)/t(n);return n}function Pn(e,t){return e*Math.sqrt(1-t*t)}const lc=["duration","bounce"],uc=["stiffness","damping","mass"];function fr(e,t){return t.some(i=>e[i]!==void 0)}function cc(e){let t={velocity:U.velocity,stiffness:U.stiffness,damping:U.damping,mass:U.mass,isResolvedFromDuration:!1,...e};if(!fr(e,uc)&&fr(e,lc))if(e.visualDuration){const i=e.visualDuration,n=2*Math.PI/(i*1.2),s=n*n,r=2*Nt(.05,1,1-(e.bounce||0))*Math.sqrt(s);t={...t,mass:U.mass,stiffness:s,damping:r}}else{const i=rc(e);t={...t,...i,mass:U.mass},t.isResolvedFromDuration=!0}return t}function Ii(e=U.visualDuration,t=U.bounce){const i=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:n,restDelta:s}=i;const r=i.keyframes[0],o=i.keyframes[i.keyframes.length-1],a={done:!1,value:r},{stiffness:l,damping:c,mass:u,duration:h,velocity:f,isResolvedFromDuration:d}=cc({...i,velocity:-Et(i.velocity||0)}),m=f||0,p=c/(2*Math.sqrt(l*u)),g=o-r,y=Et(Math.sqrt(l/u)),x=Math.abs(g)<5;n||(n=x?U.restSpeed.granular:U.restSpeed.default),s||(s=x?U.restDelta.granular:U.restDelta.default);let v;if(p<1){const _=Pn(y,p);v=T=>{const b=Math.exp(-p*y*T);return o-b*((m+p*y*g)/_*Math.sin(_*T)+g*Math.cos(_*T))}}else if(p===1)v=_=>o-Math.exp(-y*_)*(g+(m+y*g)*_);else{const _=y*Math.sqrt(p*p-1);v=T=>{const b=Math.exp(-p*y*T),S=Math.min(_*T,300);return o-b*((m+p*y*g)*Math.sinh(S)+_*g*Math.cosh(S))/_}}const w={calculatedDuration:d&&h||null,next:_=>{const T=v(_);if(d)a.done=_>=h;else{let b=_===0?m:0;p<1&&(b=_===0?Rt(m):ca(v,_,T));const S=Math.abs(b)<=n,P=Math.abs(o-T)<=s;a.done=S&&P}return a.value=a.done?o:T,a},toString:()=>{const _=Math.min(vs(w),Fi),T=ua(b=>w.next(_*b).value,_,30);return _+"ms "+T},toTransition:()=>{}};return w}Ii.applyToOptions=e=>{const t=nc(e,100,Ii);return e.ease=t.ease,e.duration=Rt(t.duration),e.type="keyframes",e};function An({keyframes:e,velocity:t=0,power:i=.8,timeConstant:n=325,bounceDamping:s=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:c=.5,restSpeed:u}){const h=e[0],f={done:!1,value:h},d=S=>a!==void 0&&S<a||l!==void 0&&S>l,m=S=>a===void 0?l:l===void 0||Math.abs(a-S)<Math.abs(l-S)?a:l;let p=i*t;const g=h+p,y=o===void 0?g:o(g);y!==g&&(p=y-h);const x=S=>-p*Math.exp(-S/n),v=S=>y+x(S),w=S=>{const P=x(S),D=v(S);f.done=Math.abs(P)<=c,f.value=f.done?y:D};let _,T;const b=S=>{d(f.value)&&(_=S,T=Ii({keyframes:[f.value,m(f.value)],velocity:ca(v,S,f.value),damping:s,stiffness:r,restDelta:c,restSpeed:u}))};return b(0),{calculatedDuration:null,next:S=>{let P=!1;return!T&&_===void 0&&(P=!0,w(S),b(S)),_!==void 0&&S>=_?T.next(S-_):(!P&&w(S),f)}}}function hc(e,t,i){const n=[],s=i||zt.mix||la,r=e.length-1;for(let o=0;o<r;o++){let a=s(e[o],e[o+1]);if(t){const l=Array.isArray(t)?t[o]||Pt:t;a=_i(l,a)}n.push(a)}return n}function fc(e,t,{clamp:i=!0,ease:n,mixer:s}={}){const r=e.length;if(us(r===t.length),r===1)return()=>t[0];if(r===2&&t[0]===t[1])return()=>t[1];const o=e[0]===e[1];e[0]>e[r-1]&&(e=[...e].reverse(),t=[...t].reverse());const a=hc(t,n,s),l=a.length,c=u=>{if(o&&u<e[0])return t[0];let h=0;if(l>1)for(;h<e.length-2&&!(u<e[h+1]);h++);const f=si(e[h],e[h+1],u);return a[h](f)};return i?u=>c(Nt(e[0],e[r-1],u)):c}function dc(e,t){const i=e[e.length-1];for(let n=1;n<=t;n++){const s=si(0,t,n);e.push(N(i,1,s))}}function pc(e){const t=[0];return dc(t,e.length-1),t}function mc(e,t){return e.map(i=>i*t)}function gc(e,t){return e.map(()=>t||Qo).splice(0,e.length-1)}function Ze({duration:e=300,keyframes:t,times:i,ease:n="easeInOut"}){const s=Cu(n)?n.map(or):or(n),r={done:!1,value:t[0]},o=mc(i&&i.length===t.length?i:pc(t),e),a=fc(o,t,{ease:Array.isArray(s)?s:gc(t,s)});return{calculatedDuration:e,next:l=>(r.value=a(l),r.done=l>=e,r)}}const yc=e=>e!==null;function xs(e,{repeat:t,repeatType:i="loop"},n,s=1){const r=e.filter(yc),a=s<0||t&&i!=="loop"&&t%2===1?0:r.length-1;return!a||n===void 0?r[a]:n}const _c={decay:An,inertia:An,tween:Ze,keyframes:Ze,spring:Ii};function ha(e){typeof e.type=="string"&&(e.type=_c[e.type])}class Ts{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,i){return this.finished.then(t,i)}}const vc=e=>e/100;class ws extends Ts{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var n,s;const{motionValue:i}=this.options;i&&i.updatedAt!==at.now()&&this.tick(at.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(s=(n=this.options).onStop)==null||s.call(n))},this.options=t,this.initAnimation(),this.play(),t.autoplay===!1&&this.pause()}initAnimation(){const{options:t}=this;ha(t);const{type:i=Ze,repeat:n=0,repeatDelay:s=0,repeatType:r,velocity:o=0}=t;let{keyframes:a}=t;const l=i||Ze;l!==Ze&&typeof a[0]!="number"&&(this.mixKeyframes=_i(vc,la(a[0],a[1])),a=[0,100]);const c=l({...t,keyframes:a});r==="mirror"&&(this.mirroredGenerator=l({...t,keyframes:[...a].reverse(),velocity:-o})),c.calculatedDuration===null&&(c.calculatedDuration=vs(c));const{calculatedDuration:u}=c;this.calculatedDuration=u,this.resolvedDuration=u+s,this.totalDuration=this.resolvedDuration*(n+1)-s,this.generator=c}updateTime(t){const i=Math.round(t-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=i}tick(t,i=!1){const{generator:n,totalDuration:s,mixKeyframes:r,mirroredGenerator:o,resolvedDuration:a,calculatedDuration:l}=this;if(this.startTime===null)return n.next(0);const{delay:c=0,keyframes:u,repeat:h,repeatType:f,repeatDelay:d,type:m,onUpdate:p,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),i?this.currentTime=t:this.updateTime(t);const y=this.currentTime-c*(this.playbackSpeed>=0?1:-1),x=this.playbackSpeed>=0?y<0:y>s;this.currentTime=Math.max(y,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=s);let v=this.currentTime,w=n;if(h){const S=Math.min(this.currentTime,s)/a;let P=Math.floor(S),D=S%1;!D&&S>=1&&(D=1),D===1&&P--,P=Math.min(P,h+1),!!(P%2)&&(f==="reverse"?(D=1-D,d&&(D-=d/a)):f==="mirror"&&(w=o)),v=Nt(0,1,D)*a}const _=x?{done:!1,value:u[0]}:w.next(v);r&&(_.value=r(_.value));let{done:T}=_;!x&&l!==null&&(T=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);const b=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&T);return b&&m!==An&&(_.value=xs(u,this.options,g,this.speed)),p&&p(_.value),b&&this.finish(),_}then(t,i){return this.finished.then(t,i)}get duration(){return Et(this.calculatedDuration)}get time(){return Et(this.currentTime)}set time(t){var i;t=Rt(t),this.currentTime=t,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),(i=this.driver)==null||i.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(at.now());const i=this.playbackSpeed!==t;this.playbackSpeed=t,i&&(this.time=Et(this.currentTime))}play(){var s,r;if(this.isStopped)return;const{driver:t=ic,startTime:i}=this.options;this.driver||(this.driver=t(o=>this.tick(o))),(r=(s=this.options).onPlay)==null||r.call(s);const n=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=n):this.holdTime!==null?this.startTime=n-this.holdTime:this.startTime||(this.startTime=i??n),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(at.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var t,i;this.notifyFinished(),this.teardown(),this.state="finished",(i=(t=this.options).onComplete)==null||i.call(t)}cancel(){var t,i;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(i=(t=this.options).onCancel)==null||i.call(t)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){var i;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(i=this.driver)==null||i.stop(),t.observe(this)}}function xc(e){for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}const he=e=>e*180/Math.PI,Cn=e=>{const t=he(Math.atan2(e[1],e[0]));return Mn(t)},Tc={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:Cn,rotateZ:Cn,skewX:e=>he(Math.atan(e[1])),skewY:e=>he(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},Mn=e=>(e=e%360,e<0&&(e+=360),e),dr=Cn,pr=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),mr=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),wc={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:pr,scaleY:mr,scale:e=>(pr(e)+mr(e))/2,rotateX:e=>Mn(he(Math.atan2(e[6],e[5]))),rotateY:e=>Mn(he(Math.atan2(-e[2],e[0]))),rotateZ:dr,rotate:dr,skewX:e=>he(Math.atan(e[4])),skewY:e=>he(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function Dn(e){return e.includes("scale")?1:0}function Rn(e,t){if(!e||e==="none")return Dn(t);const i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let n,s;if(i)n=wc,s=i;else{const a=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=Tc,s=a}if(!s)return Dn(t);const r=n[t],o=s[1].split(",").map(bc);return typeof r=="function"?r(o):o[r]}const Sc=(e,t)=>{const{transform:i="none"}=getComputedStyle(e);return Rn(i,t)};function bc(e){return parseFloat(e.trim())}const je=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Ue=new Set(je),gr=e=>e===ze||e===M,Pc=new Set(["x","y","z"]),Ac=je.filter(e=>!Pc.has(e));function Cc(e){const t=[];return Ac.forEach(i=>{const n=e.getValue(i);n!==void 0&&(t.push([i,n.get()]),n.set(i.startsWith("scale")?1:0))}),t}const de={width:({x:e},{paddingLeft:t="0",paddingRight:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),height:({y:e},{paddingTop:t="0",paddingBottom:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>Rn(t,"x"),y:(e,{transform:t})=>Rn(t,"y")};de.translateX=de.x;de.translateY=de.y;const pe=new Set;let En=!1,Vn=!1,kn=!1;function fa(){if(Vn){const e=Array.from(pe).filter(n=>n.needsMeasurement),t=new Set(e.map(n=>n.element)),i=new Map;t.forEach(n=>{const s=Cc(n);s.length&&(i.set(n,s),n.render())}),e.forEach(n=>n.measureInitialState()),t.forEach(n=>{n.render();const s=i.get(n);s&&s.forEach(([r,o])=>{var a;(a=n.getValue(r))==null||a.set(o)})}),e.forEach(n=>n.measureEndState()),e.forEach(n=>{n.suspendedScrollY!==void 0&&window.scrollTo(0,n.suspendedScrollY)})}Vn=!1,En=!1,pe.forEach(e=>e.complete(kn)),pe.clear()}function da(){pe.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Vn=!0)})}function Mc(){kn=!0,da(),fa(),kn=!1}class Ss{constructor(t,i,n,s,r,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=i,this.name=n,this.motionValue=s,this.element=r,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(pe.add(this),En||(En=!0,j.read(da),j.resolveKeyframes(fa))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:i,element:n,motionValue:s}=this;if(t[0]===null){const r=s==null?void 0:s.get(),o=t[t.length-1];if(r!==void 0)t[0]=r;else if(n&&i){const a=n.readValue(i,o);a!=null&&(t[0]=a)}t[0]===void 0&&(t[0]=o),s&&r===void 0&&s.set(t[0])}xc(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),pe.delete(this)}cancel(){this.state==="scheduled"&&(pe.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const Dc=e=>e.startsWith("--");function Rc(e,t,i){Dc(t)?e.style.setProperty(t,i):e.style[t]=i}const Ec=cs(()=>window.ScrollTimeline!==void 0),Vc={};function kc(e,t){const i=cs(e);return()=>Vc[t]??i()}const pa=kc(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),Ke=([e,t,i,n])=>`cubic-bezier(${e}, ${t}, ${i}, ${n})`,yr={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ke([0,.65,.55,1]),circOut:Ke([.55,0,1,.45]),backIn:Ke([.31,.01,.66,-.59]),backOut:Ke([.33,1.53,.69,.99])};function ma(e,t){if(e)return typeof e=="function"?pa()?ua(e,t):"ease-out":Jo(e)?Ke(e):Array.isArray(e)?e.map(i=>ma(i,t)||yr.easeOut):yr[e]}function Oc(e,t,i,{delay:n=0,duration:s=300,repeat:r=0,repeatType:o="loop",ease:a="easeOut",times:l}={},c=void 0){const u={[t]:i};l&&(u.offset=l);const h=ma(a,s);Array.isArray(h)&&(u.easing=h);const f={delay:n,duration:s,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:r+1,direction:o==="reverse"?"alternate":"normal"};return c&&(f.pseudoElement=c),e.animate(u,f)}function ga(e){return typeof e=="function"&&"applyToOptions"in e}function Lc({type:e,...t}){return ga(e)&&pa()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}class Fc extends Ts{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:i,name:n,keyframes:s,pseudoElement:r,allowFlatten:o=!1,finalKeyframe:a,onComplete:l}=t;this.isPseudoElement=!!r,this.allowFlatten=o,this.options=t,us(typeof t.type!="string");const c=Lc(t);this.animation=Oc(i,n,s,c,r),c.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){const u=xs(s,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(u):Rc(i,n,u),this.animation.cancel()}l==null||l(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var t,i;(i=(t=this.animation).finish)==null||i.call(t)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;t==="idle"||t==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var t,i;this.isPseudoElement||(i=(t=this.animation).commitStyles)==null||i.call(t)}get duration(){var i,n;const t=((n=(i=this.animation.effect)==null?void 0:i.getComputedTiming)==null?void 0:n.call(i).duration)||0;return Et(Number(t))}get time(){return Et(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=Rt(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:i}){var n;return this.allowFlatten&&((n=this.animation.effect)==null||n.updateTiming({easing:"linear"})),this.animation.onfinish=null,t&&Ec()?(this.animation.timeline=t,Pt):i(this)}}const ya={anticipate:Go,backInOut:Ho,circInOut:Zo};function Ic(e){return e in ya}function Bc(e){typeof e.ease=="string"&&Ic(e.ease)&&(e.ease=ya[e.ease])}const _r=10;class Nc extends Fc{constructor(t){Bc(t),ha(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:i,onUpdate:n,onComplete:s,element:r,...o}=this.options;if(!i)return;if(t!==void 0){i.set(t);return}const a=new ws({...o,autoplay:!1}),l=Rt(this.finishedTime??this.time);i.setWithVelocity(a.sample(l-_r).value,a.sample(l).value,_r),a.stop()}}const vr=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(Qt.test(e)||e==="0")&&!e.startsWith("url("));function zc(e){const t=e[0];if(e.length===1)return!0;for(let i=0;i<e.length;i++)if(e[i]!==t)return!0}function jc(e,t,i,n){const s=e[0];if(s===null)return!1;if(t==="display"||t==="visibility")return!0;const r=e[e.length-1],o=vr(s,t),a=vr(r,t);return!o||!a?!1:zc(e)||(i==="spring"||ga(i))&&n}function bs(e){return jo(e)&&"offsetHeight"in e}const Uc=new Set(["opacity","clipPath","filter","transform"]),Wc=cs(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function Yc(e){var c;const{motionValue:t,name:i,repeatDelay:n,repeatType:s,damping:r,type:o}=e;if(!bs((c=t==null?void 0:t.owner)==null?void 0:c.current))return!1;const{onUpdate:a,transformTemplate:l}=t.owner.getProps();return Wc()&&i&&Uc.has(i)&&(i!=="transform"||!l)&&!a&&!n&&s!=="mirror"&&r!==0&&o!=="inertia"}const Xc=40;class $c extends Ts{constructor({autoplay:t=!0,delay:i=0,type:n="keyframes",repeat:s=0,repeatDelay:r=0,repeatType:o="loop",keyframes:a,name:l,motionValue:c,element:u,...h}){var m;super(),this.stop=()=>{var p,g;this._animation&&(this._animation.stop(),(p=this.stopTimeline)==null||p.call(this)),(g=this.keyframeResolver)==null||g.cancel()},this.createdAt=at.now();const f={autoplay:t,delay:i,type:n,repeat:s,repeatDelay:r,repeatType:o,name:l,motionValue:c,element:u,...h},d=(u==null?void 0:u.KeyframeResolver)||Ss;this.keyframeResolver=new d(a,(p,g,y)=>this.onKeyframesResolved(p,g,f,!y),l,c,u),(m=this.keyframeResolver)==null||m.scheduleResolve()}onKeyframesResolved(t,i,n,s){this.keyframeResolver=void 0;const{name:r,type:o,velocity:a,delay:l,isHandoff:c,onUpdate:u}=n;this.resolvedAt=at.now(),jc(t,r,o,a)||((zt.instantAnimations||!l)&&(u==null||u(xs(t,n,i))),t[0]=t[t.length-1],n.duration=0,n.repeat=0);const f={startTime:s?this.resolvedAt?this.resolvedAt-this.createdAt>Xc?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:i,...n,keyframes:t},d=!c&&Yc(f)?new Nc({...f,element:f.motionValue.owner.current}):new ws(f);d.finished.then(()=>this.notifyFinished()).catch(Pt),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,i){return this.finished.finally(t).then(()=>{})}get animation(){var t;return this._animation||((t=this.keyframeResolver)==null||t.resume(),Mc()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var t;this._animation&&this.animation.cancel(),(t=this.keyframeResolver)==null||t.cancel()}}const Kc=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Hc(e){const t=Kc.exec(e);if(!t)return[,];const[,i,n,s]=t;return[`--${i??n}`,s]}function _a(e,t,i=1){const[n,s]=Hc(e);if(!n)return;const r=window.getComputedStyle(t).getPropertyValue(n);if(r){const o=r.trim();return zo(o)?parseFloat(o):o}return ms(s)?_a(s,t,i+1):s}function Ps(e,t){return(e==null?void 0:e[t])??(e==null?void 0:e.default)??e}const va=new Set(["width","height","top","left","right","bottom",...je]),Gc={test:e=>e==="auto",parse:e=>e},xa=e=>t=>t.test(e),Ta=[ze,M,Vt,Xt,zu,Nu,Gc],xr=e=>Ta.find(xa(e));function qc(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||Uo(e):!0}const Zc=new Set(["brightness","contrast","saturate","opacity"]);function Qc(e){const[t,i]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[n]=i.match(gs)||[];if(!n)return e;const s=i.replace(n,"");let r=Zc.has(t)?1:0;return n!==i&&(r*=100),t+"("+r+s+")"}const Jc=/\b([a-z-]*)\(.*?\)/gu,On={...Qt,getAnimatableNone:e=>{const t=e.match(Jc);return t?t.map(Qc).join(" "):e}},Tr={...ze,transform:Math.round},th={rotate:Xt,rotateX:Xt,rotateY:Xt,rotateZ:Xt,scale:bi,scaleX:bi,scaleY:bi,scaleZ:bi,skew:Xt,skewX:Xt,skewY:Xt,distance:M,translateX:M,translateY:M,translateZ:M,x:M,y:M,z:M,perspective:M,transformPerspective:M,opacity:ri,originX:lr,originY:lr,originZ:M},As={borderWidth:M,borderTopWidth:M,borderRightWidth:M,borderBottomWidth:M,borderLeftWidth:M,borderRadius:M,radius:M,borderTopLeftRadius:M,borderTopRightRadius:M,borderBottomRightRadius:M,borderBottomLeftRadius:M,width:M,maxWidth:M,height:M,maxHeight:M,top:M,right:M,bottom:M,left:M,padding:M,paddingTop:M,paddingRight:M,paddingBottom:M,paddingLeft:M,margin:M,marginTop:M,marginRight:M,marginBottom:M,marginLeft:M,backgroundPositionX:M,backgroundPositionY:M,...th,zIndex:Tr,fillOpacity:ri,strokeOpacity:ri,numOctaves:Tr},eh={...As,color:G,backgroundColor:G,outlineColor:G,fill:G,stroke:G,borderColor:G,borderTopColor:G,borderRightColor:G,borderBottomColor:G,borderLeftColor:G,filter:On,WebkitFilter:On},wa=e=>eh[e];function Sa(e,t){let i=wa(e);return i!==On&&(i=Qt),i.getAnimatableNone?i.getAnimatableNone(t):void 0}const ih=new Set(["auto","none","0"]);function nh(e,t,i){let n=0,s;for(;n<e.length&&!s;){const r=e[n];typeof r=="string"&&!ih.has(r)&&oi(r).values.length&&(s=e[n]),n++}if(s&&i)for(const r of t)e[r]=Sa(i,s)}class sh extends Ss{constructor(t,i,n,s,r){super(t,i,n,s,r,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:i,name:n}=this;if(!i||!i.current)return;super.readKeyframes();for(let l=0;l<t.length;l++){let c=t[l];if(typeof c=="string"&&(c=c.trim(),ms(c))){const u=_a(c,i.current);u!==void 0&&(t[l]=u),l===t.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!va.has(n)||t.length!==2)return;const[s,r]=t,o=xr(s),a=xr(r);if(o!==a)if(gr(o)&&gr(a))for(let l=0;l<t.length;l++){const c=t[l];typeof c=="string"&&(t[l]=parseFloat(c))}else de[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:i}=this,n=[];for(let s=0;s<t.length;s++)(t[s]===null||qc(t[s]))&&n.push(s);n.length&&nh(t,n,i)}measureInitialState(){const{element:t,unresolvedKeyframes:i,name:n}=this;if(!t||!t.current)return;n==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=de[n](t.measureViewportBox(),window.getComputedStyle(t.current)),i[0]=this.measuredOrigin;const s=i[i.length-1];s!==void 0&&t.getValue(n,s).jump(s,!1)}measureEndState(){var a;const{element:t,name:i,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const s=t.getValue(i);s&&s.jump(this.measuredOrigin,!1);const r=n.length-1,o=n[r];n[r]=de[i](t.measureViewportBox(),window.getComputedStyle(t.current)),o!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=o),(a=this.removedTransforms)!=null&&a.length&&this.removedTransforms.forEach(([l,c])=>{t.getValue(l).set(c)}),this.resolveNoneKeyframes()}}function rh(e,t,i){if(e instanceof EventTarget)return[e];if(typeof e=="string"){let n=document;const s=(i==null?void 0:i[e])??n.querySelectorAll(e);return s?Array.from(s):[]}return Array.from(e)}const ba=(e,t)=>t&&typeof e=="number"?t.transform(e):e,wr=30,oh=e=>!isNaN(parseFloat(e));class ah{constructor(t,i={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(n,s=!0)=>{var o,a;const r=at.now();if(this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(n),this.current!==this.prev&&((o=this.events.change)==null||o.notify(this.current),this.dependents))for(const l of this.dependents)l.dirty();s&&((a=this.events.renderRequest)==null||a.notify(this.current))},this.hasAnimated=!1,this.setCurrent(t),this.owner=i.owner}setCurrent(t){this.current=t,this.updatedAt=at.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=oh(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,i){this.events[t]||(this.events[t]=new hs);const n=this.events[t].add(i);return t==="change"?()=>{n(),j.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,i){this.passiveEffect=t,this.stopPassiveEffect=i}set(t,i=!0){!i||!this.passiveEffect?this.updateAndNotify(t,i):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,i,n){this.set(i),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,i=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,i&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var t;(t=this.events.change)==null||t.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=at.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>wr)return 0;const i=Math.min(this.updatedAt-this.prevUpdatedAt,wr);return Wo(parseFloat(this.current)-parseFloat(this.prevFrameValue),i)}start(t){return this.stop(),new Promise(i=>{this.hasAnimated=!0,this.animation=t(i),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var t,i;(t=this.dependents)==null||t.clear(),(i=this.events.destroy)==null||i.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Ve(e,t){return new ah(e,t)}const{schedule:Cs}=ta(queueMicrotask,!1),At={x:!1,y:!1};function Pa(){return At.x||At.y}function lh(e){return e==="x"||e==="y"?At[e]?null:(At[e]=!0,()=>{At[e]=!1}):At.x||At.y?null:(At.x=At.y=!0,()=>{At.x=At.y=!1})}function Aa(e,t){const i=rh(e),n=new AbortController,s={passive:!0,...t,signal:n.signal};return[i,s,()=>n.abort()]}function Sr(e){return!(e.pointerType==="touch"||Pa())}function uh(e,t,i={}){const[n,s,r]=Aa(e,i),o=a=>{if(!Sr(a))return;const{target:l}=a,c=t(l,a);if(typeof c!="function"||!l)return;const u=h=>{Sr(h)&&(c(h),l.removeEventListener("pointerleave",u))};l.addEventListener("pointerleave",u,s)};return n.forEach(a=>{a.addEventListener("pointerenter",o,s)}),r}const Ca=(e,t)=>t?e===t?!0:Ca(e,t.parentElement):!1,Ms=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,ch=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function hh(e){return ch.has(e.tagName)||e.tabIndex!==-1}const Di=new WeakSet;function br(e){return t=>{t.key==="Enter"&&e(t)}}function an(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const fh=(e,t)=>{const i=e.currentTarget;if(!i)return;const n=br(()=>{if(Di.has(i))return;an(i,"down");const s=br(()=>{an(i,"up")}),r=()=>an(i,"cancel");i.addEventListener("keyup",s,t),i.addEventListener("blur",r,t)});i.addEventListener("keydown",n,t),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),t)};function Pr(e){return Ms(e)&&!Pa()}function dh(e,t,i={}){const[n,s,r]=Aa(e,i),o=a=>{const l=a.currentTarget;if(!Pr(a))return;Di.add(l);const c=t(l,a),u=(d,m)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",f),Di.has(l)&&Di.delete(l),Pr(d)&&typeof c=="function"&&c(d,{success:m})},h=d=>{u(d,l===window||l===document||i.useGlobalTarget||Ca(l,d.target))},f=d=>{u(d,!1)};window.addEventListener("pointerup",h,s),window.addEventListener("pointercancel",f,s)};return n.forEach(a=>{(i.useGlobalTarget?window:a).addEventListener("pointerdown",o,s),bs(a)&&(a.addEventListener("focus",c=>fh(c,s)),!hh(a)&&!a.hasAttribute("tabindex")&&(a.tabIndex=0))}),r}function Ma(e){return jo(e)&&"ownerSVGElement"in e}function ph(e){return Ma(e)&&e.tagName==="svg"}const nt=e=>!!(e&&e.getVelocity),mh=[...Ta,G,Qt],gh=e=>mh.find(xa(e)),Ds=A.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class yh extends A.Component{getSnapshotBeforeUpdate(t){const i=this.props.childRef.current;if(i&&t.isPresent&&!this.props.isPresent){const n=i.offsetParent,s=bs(n)&&n.offsetWidth||0,r=this.props.sizeRef.current;r.height=i.offsetHeight||0,r.width=i.offsetWidth||0,r.top=i.offsetTop,r.left=i.offsetLeft,r.right=s-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function _h({children:e,isPresent:t,anchorX:i}){const n=A.useId(),s=A.useRef(null),r=A.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:o}=A.useContext(Ds);return A.useInsertionEffect(()=>{const{width:a,height:l,top:c,left:u,right:h}=r.current;if(t||!s.current||!a||!l)return;const f=i==="left"?`left: ${u}`:`right: ${h}`;s.current.dataset.motionPopId=n;const d=document.createElement("style");return o&&(d.nonce=o),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${a}px !important;
            height: ${l}px !important;
            ${f}px !important;
            top: ${c}px !important;
          }
        `),()=>{document.head.contains(d)&&document.head.removeChild(d)}},[t]),Bt.jsx(yh,{isPresent:t,childRef:s,sizeRef:r,children:A.cloneElement(e,{ref:s})})}const vh=({children:e,initial:t,isPresent:i,onExitComplete:n,custom:s,presenceAffectsLayout:r,mode:o,anchorX:a})=>{const l=rs(xh),c=A.useId();let u=!0,h=A.useMemo(()=>(u=!1,{id:c,initial:t,isPresent:i,custom:s,onExitComplete:f=>{l.set(f,!0);for(const d of l.values())if(!d)return;n&&n()},register:f=>(l.set(f,!1),()=>l.delete(f))}),[i,l,n]);return r&&u&&(h={...h}),A.useMemo(()=>{l.forEach((f,d)=>l.set(d,!1))},[i]),A.useEffect(()=>{!i&&!l.size&&n&&n()},[i]),o==="popLayout"&&(e=Bt.jsx(_h,{isPresent:i,anchorX:a,children:e})),Bt.jsx(Ki.Provider,{value:h,children:e})};function xh(){return new Map}function Da(e=!0){const t=A.useContext(Ki);if(t===null)return[!0,null];const{isPresent:i,onExitComplete:n,register:s}=t,r=A.useId();A.useEffect(()=>{if(e)return s(r)},[e]);const o=A.useCallback(()=>e&&n&&n(r),[r,n,e]);return!i&&n?[!1,o]:[!0]}const Pi=e=>e.key||"";function Ar(e){const t=[];return A.Children.forEach(e,i=>{A.isValidElement(i)&&t.push(i)}),t}const Em=({children:e,custom:t,initial:i=!0,onExitComplete:n,presenceAffectsLayout:s=!0,mode:r="sync",propagate:o=!1,anchorX:a="left"})=>{const[l,c]=Da(o),u=A.useMemo(()=>Ar(e),[e]),h=o&&!l?[]:u.map(Pi),f=A.useRef(!0),d=A.useRef(u),m=rs(()=>new Map),[p,g]=A.useState(u),[y,x]=A.useState(u);No(()=>{f.current=!1,d.current=u;for(let _=0;_<y.length;_++){const T=Pi(y[_]);h.includes(T)?m.delete(T):m.get(T)!==!0&&m.set(T,!1)}},[y,h.length,h.join("-")]);const v=[];if(u!==p){let _=[...u];for(let T=0;T<y.length;T++){const b=y[T],S=Pi(b);h.includes(S)||(_.splice(T,0,b),v.push(b))}return r==="wait"&&v.length&&(_=v),x(Ar(_)),g(u),null}const{forceRender:w}=A.useContext(ss);return Bt.jsx(Bt.Fragment,{children:y.map(_=>{const T=Pi(_),b=o&&!l?!1:u===y||h.includes(T),S=()=>{if(m.has(T))m.set(T,!0);else return;let P=!0;m.forEach(D=>{D||(P=!1)}),P&&(w==null||w(),x(d.current),o&&(c==null||c()),n&&n())};return Bt.jsx(vh,{isPresent:b,initial:!f.current||i?void 0:!1,custom:t,presenceAffectsLayout:s,mode:r,onExitComplete:b?void 0:S,anchorX:a,children:_},T)})})},Ra=A.createContext({strict:!1}),Cr={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ke={};for(const e in Cr)ke[e]={isEnabled:t=>Cr[e].some(i=>!!t[i])};function Th(e){for(const t in e)ke[t]={...ke[t],...e[t]}}const wh=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Bi(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||wh.has(e)}let Ea=e=>!Bi(e);function Sh(e){typeof e=="function"&&(Ea=t=>t.startsWith("on")?!Bi(t):e(t))}try{Sh(require("@emotion/is-prop-valid").default)}catch{}function bh(e,t,i){const n={};for(const s in e)s==="values"&&typeof e.values=="object"||(Ea(s)||i===!0&&Bi(s)||!t&&!Bi(s)||e.draggable&&s.startsWith("onDrag"))&&(n[s]=e[s]);return n}function Ph(e){if(typeof Proxy>"u")return e;const t=new Map,i=(...n)=>e(...n);return new Proxy(i,{get:(n,s)=>s==="create"?e:(t.has(s)||t.set(s,e(s)),t.get(s))})}const Hi=A.createContext({});function Gi(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}function ai(e){return typeof e=="string"||Array.isArray(e)}const Rs=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Es=["initial",...Rs];function qi(e){return Gi(e.animate)||Es.some(t=>ai(e[t]))}function Va(e){return!!(qi(e)||e.variants)}function Ah(e,t){if(qi(e)){const{initial:i,animate:n}=e;return{initial:i===!1||ai(i)?i:void 0,animate:ai(n)?n:void 0}}return e.inherit!==!1?t:{}}function Ch(e){const{initial:t,animate:i}=Ah(e,A.useContext(Hi));return A.useMemo(()=>({initial:t,animate:i}),[Mr(t),Mr(i)])}function Mr(e){return Array.isArray(e)?e.join(" "):e}const Mh=Symbol.for("motionComponentSymbol");function Se(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function Dh(e,t,i){return A.useCallback(n=>{n&&e.onMount&&e.onMount(n),t&&(n?t.mount(n):t.unmount()),i&&(typeof i=="function"?i(n):Se(i)&&(i.current=n))},[t])}const Vs=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Rh="framerAppearId",ka="data-"+Vs(Rh),Oa=A.createContext({});function Eh(e,t,i,n,s){var p,g;const{visualElement:r}=A.useContext(Hi),o=A.useContext(Ra),a=A.useContext(Ki),l=A.useContext(Ds).reducedMotion,c=A.useRef(null);n=n||o.renderer,!c.current&&n&&(c.current=n(e,{visualState:t,parent:r,props:i,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:l}));const u=c.current,h=A.useContext(Oa);u&&!u.projection&&s&&(u.type==="html"||u.type==="svg")&&Vh(c.current,i,s,h);const f=A.useRef(!1);A.useInsertionEffect(()=>{u&&f.current&&u.update(i,a)});const d=i[ka],m=A.useRef(!!d&&!((p=window.MotionHandoffIsComplete)!=null&&p.call(window,d))&&((g=window.MotionHasOptimisedAnimation)==null?void 0:g.call(window,d)));return No(()=>{u&&(f.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),Cs.render(u.render),m.current&&u.animationState&&u.animationState.animateChanges())}),A.useEffect(()=>{u&&(!m.current&&u.animationState&&u.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{var y;(y=window.MotionHandoffMarkAsComplete)==null||y.call(window,d)}),m.current=!1))}),u}function Vh(e,t,i,n){const{layoutId:s,layout:r,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:c,layoutCrossfade:u}=t;e.projection=new i(e.latestValues,t["data-framer-portal-id"]?void 0:La(e.parent)),e.projection.setOptions({layoutId:s,layout:r,alwaysMeasureLayout:!!o||a&&Se(a),visualElement:e,animationType:typeof r=="string"?r:"both",initialPromotionConfig:n,crossfade:u,layoutScroll:l,layoutRoot:c})}function La(e){if(e)return e.options.allowProjection!==!1?e.projection:La(e.parent)}function kh({preloadedFeatures:e,createVisualElement:t,useRender:i,useVisualState:n,Component:s}){e&&Th(e);function r(a,l){let c;const u={...A.useContext(Ds),...a,layoutId:Oh(a)},{isStatic:h}=u,f=Ch(a),d=n(a,h);if(!h&&os){Lh();const m=Fh(u);c=m.MeasureLayout,f.visualElement=Eh(s,d,u,t,m.ProjectionNode)}return Bt.jsxs(Hi.Provider,{value:f,children:[c&&f.visualElement?Bt.jsx(c,{visualElement:f.visualElement,...u}):null,i(s,a,Dh(d,f.visualElement,l),d,h,f.visualElement)]})}r.displayName=`motion.${typeof s=="string"?s:`create(${s.displayName??s.name??""})`}`;const o=A.forwardRef(r);return o[Mh]=s,o}function Oh({layoutId:e}){const t=A.useContext(ss).id;return t&&e!==void 0?t+"-"+e:e}function Lh(e,t){A.useContext(Ra).strict}function Fh(e){const{drag:t,layout:i}=ke;if(!t&&!i)return{};const n={...t,...i};return{MeasureLayout:t!=null&&t.isEnabled(e)||i!=null&&i.isEnabled(e)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}const li={};function Ih(e){for(const t in e)li[t]=e[t],ps(t)&&(li[t].isCSSVariable=!0)}function Fa(e,{layout:t,layoutId:i}){return Ue.has(e)||e.startsWith("origin")||(t||i!==void 0)&&(!!li[e]||e==="opacity")}const Bh={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Nh=je.length;function zh(e,t,i){let n="",s=!0;for(let r=0;r<Nh;r++){const o=je[r],a=e[o];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(o.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||i){const c=ba(a,As[o]);if(!l){s=!1;const u=Bh[o]||o;n+=`${u}(${c}) `}i&&(t[o]=c)}}return n=n.trim(),i?n=i(t,s?"":n):s&&(n="none"),n}function ks(e,t,i){const{style:n,vars:s,transformOrigin:r}=e;let o=!1,a=!1;for(const l in t){const c=t[l];if(Ue.has(l)){o=!0;continue}else if(ps(l)){s[l]=c;continue}else{const u=ba(c,As[l]);l.startsWith("origin")?(a=!0,r[l]=u):n[l]=u}}if(t.transform||(o||i?n.transform=zh(t,e.transform,i):n.transform&&(n.transform="none")),a){const{originX:l="50%",originY:c="50%",originZ:u=0}=r;n.transformOrigin=`${l} ${c} ${u}`}}const Os=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Ia(e,t,i){for(const n in t)!nt(t[n])&&!Fa(n,i)&&(e[n]=t[n])}function jh({transformTemplate:e},t){return A.useMemo(()=>{const i=Os();return ks(i,t,e),Object.assign({},i.vars,i.style)},[t])}function Uh(e,t){const i=e.style||{},n={};return Ia(n,i,e),Object.assign(n,jh(e,t)),n}function Wh(e,t){const i={},n=Uh(e,t);return e.drag&&e.dragListener!==!1&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(i.tabIndex=0),i.style=n,i}const Yh={offset:"stroke-dashoffset",array:"stroke-dasharray"},Xh={offset:"strokeDashoffset",array:"strokeDasharray"};function $h(e,t,i=1,n=0,s=!0){e.pathLength=1;const r=s?Yh:Xh;e[r.offset]=M.transform(-n);const o=M.transform(t),a=M.transform(i);e[r.array]=`${o} ${a}`}function Ba(e,{attrX:t,attrY:i,attrScale:n,pathLength:s,pathSpacing:r=1,pathOffset:o=0,...a},l,c,u){if(ks(e,a,c),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:h,style:f}=e;h.transform&&(f.transform=h.transform,delete h.transform),(f.transform||h.transformOrigin)&&(f.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),f.transform&&(f.transformBox=(u==null?void 0:u.transformBox)??"fill-box",delete h.transformBox),t!==void 0&&(h.x=t),i!==void 0&&(h.y=i),n!==void 0&&(h.scale=n),s!==void 0&&$h(h,s,r,o,!1)}const Na=()=>({...Os(),attrs:{}}),za=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Kh(e,t,i,n){const s=A.useMemo(()=>{const r=Na();return Ba(r,t,za(n),e.transformTemplate,e.style),{...r.attrs,style:{...r.style}}},[t]);if(e.style){const r={};Ia(r,e.style,e),s.style={...r,...s.style}}return s}const Hh=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Ls(e){return typeof e!="string"||e.includes("-")?!1:!!(Hh.indexOf(e)>-1||/[A-Z]/u.test(e))}function Gh(e=!1){return(i,n,s,{latestValues:r},o)=>{const l=(Ls(i)?Kh:Wh)(n,r,o,i),c=bh(n,typeof i=="string",e),u=i!==A.Fragment?{...c,...l,ref:s}:{},{children:h}=n,f=A.useMemo(()=>nt(h)?h.get():h,[h]);return A.createElement(i,{...u,children:f})}}function Dr(e){const t=[{},{}];return e==null||e.values.forEach((i,n)=>{t[0][n]=i.get(),t[1][n]=i.getVelocity()}),t}function Fs(e,t,i,n){if(typeof t=="function"){const[s,r]=Dr(n);t=t(i!==void 0?i:e.custom,s,r)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[s,r]=Dr(n);t=t(i!==void 0?i:e.custom,s,r)}return t}function Ri(e){return nt(e)?e.get():e}function qh({scrapeMotionValuesFromProps:e,createRenderState:t},i,n,s){return{latestValues:Zh(i,n,s,e),renderState:t()}}const ja=e=>(t,i)=>{const n=A.useContext(Hi),s=A.useContext(Ki),r=()=>qh(e,t,n,s);return i?r():rs(r)};function Zh(e,t,i,n){const s={},r=n(e,{});for(const f in r)s[f]=Ri(r[f]);let{initial:o,animate:a}=e;const l=qi(e),c=Va(e);t&&c&&!l&&e.inherit!==!1&&(o===void 0&&(o=t.initial),a===void 0&&(a=t.animate));let u=i?i.initial===!1:!1;u=u||o===!1;const h=u?a:o;if(h&&typeof h!="boolean"&&!Gi(h)){const f=Array.isArray(h)?h:[h];for(let d=0;d<f.length;d++){const m=Fs(e,f[d]);if(m){const{transitionEnd:p,transition:g,...y}=m;for(const x in y){let v=y[x];if(Array.isArray(v)){const w=u?v.length-1:0;v=v[w]}v!==null&&(s[x]=v)}for(const x in p)s[x]=p[x]}}}return s}function Is(e,t,i){var r;const{style:n}=e,s={};for(const o in n)(nt(n[o])||t.style&&nt(t.style[o])||Fa(o,e)||((r=i==null?void 0:i.getValue(o))==null?void 0:r.liveStyle)!==void 0)&&(s[o]=n[o]);return s}const Qh={useVisualState:ja({scrapeMotionValuesFromProps:Is,createRenderState:Os})};function Ua(e,t,i){const n=Is(e,t,i);for(const s in e)if(nt(e[s])||nt(t[s])){const r=je.indexOf(s)!==-1?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s;n[r]=e[s]}return n}const Jh={useVisualState:ja({scrapeMotionValuesFromProps:Ua,createRenderState:Na})};function tf(e,t){return function(n,{forwardMotionProps:s}={forwardMotionProps:!1}){const o={...Ls(n)?Jh:Qh,preloadedFeatures:e,useRender:Gh(s),createVisualElement:t,Component:n};return kh(o)}}function ui(e,t,i){const n=e.getProps();return Fs(n,t,i!==void 0?i:n.custom,e)}const Ln=e=>Array.isArray(e);function ef(e,t,i){e.hasValue(t)?e.getValue(t).set(i):e.addValue(t,Ve(i))}function nf(e){return Ln(e)?e[e.length-1]||0:e}function sf(e,t){const i=ui(e,t);let{transitionEnd:n={},transition:s={},...r}=i||{};r={...r,...n};for(const o in r){const a=nf(r[o]);ef(e,o,a)}}function rf(e){return!!(nt(e)&&e.add)}function Fn(e,t){const i=e.getValue("willChange");if(rf(i))return i.add(t);if(!i&&zt.WillChange){const n=new zt.WillChange("auto");e.addValue("willChange",n),n.add(t)}}function Wa(e){return e.props[ka]}const of=e=>e!==null;function af(e,{repeat:t,repeatType:i="loop"},n){const s=e.filter(of),r=t&&i!=="loop"&&t%2===1?0:s.length-1;return s[r]}const lf={type:"spring",stiffness:500,damping:25,restSpeed:10},uf=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),cf={type:"keyframes",duration:.8},hf={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ff=(e,{keyframes:t})=>t.length>2?cf:Ue.has(e)?e.startsWith("scale")?uf(t[1]):lf:hf;function df({when:e,delay:t,delayChildren:i,staggerChildren:n,staggerDirection:s,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length}const Bs=(e,t,i,n={},s,r)=>o=>{const a=Ps(n,e)||{},l=a.delay||n.delay||0;let{elapsed:c=0}=n;c=c-Rt(l);const u={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-c,onUpdate:f=>{t.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:r?void 0:s};df(a)||Object.assign(u,ff(e,u)),u.duration&&(u.duration=Rt(u.duration)),u.repeatDelay&&(u.repeatDelay=Rt(u.repeatDelay)),u.from!==void 0&&(u.keyframes[0]=u.from);let h=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(u.duration=0,u.delay===0&&(h=!0)),(zt.instantAnimations||zt.skipAnimations)&&(h=!0,u.duration=0,u.delay=0),u.allowFlatten=!a.type&&!a.ease,h&&!r&&t.get()!==void 0){const f=af(u.keyframes,a);if(f!==void 0){j.update(()=>{u.onUpdate(f),u.onComplete()});return}}return a.isSync?new ws(u):new $c(u)};function pf({protectedKeys:e,needsAnimating:t},i){const n=e.hasOwnProperty(i)&&t[i]!==!0;return t[i]=!1,n}function Ya(e,t,{delay:i=0,transitionOverride:n,type:s}={}){let{transition:r=e.getDefaultTransition(),transitionEnd:o,...a}=t;n&&(r=n);const l=[],c=s&&e.animationState&&e.animationState.getState()[s];for(const u in a){const h=e.getValue(u,e.latestValues[u]??null),f=a[u];if(f===void 0||c&&pf(c,u))continue;const d={delay:i,...Ps(r||{},u)},m=h.get();if(m!==void 0&&!h.isAnimating&&!Array.isArray(f)&&f===m&&!d.velocity)continue;let p=!1;if(window.MotionHandoffAnimation){const y=Wa(e);if(y){const x=window.MotionHandoffAnimation(y,u,j);x!==null&&(d.startTime=x,p=!0)}}Fn(e,u),h.start(Bs(u,h,f,e.shouldReduceMotion&&va.has(u)?{type:!1}:d,e,p));const g=h.animation;g&&l.push(g)}return o&&Promise.all(l).then(()=>{j.update(()=>{o&&sf(e,o)})}),l}function In(e,t,i={}){var l;const n=ui(e,t,i.type==="exit"?(l=e.presenceContext)==null?void 0:l.custom:void 0);let{transition:s=e.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(s=i.transitionOverride);const r=n?()=>Promise.all(Ya(e,n,i)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:h,staggerDirection:f}=s;return mf(e,t,u+c,h,f,i)}:()=>Promise.resolve(),{when:a}=s;if(a){const[c,u]=a==="beforeChildren"?[r,o]:[o,r];return c().then(()=>u())}else return Promise.all([r(),o(i.delay)])}function mf(e,t,i=0,n=0,s=1,r){const o=[],a=(e.variantChildren.size-1)*n,l=s===1?(c=0)=>c*n:(c=0)=>a-c*n;return Array.from(e.variantChildren).sort(gf).forEach((c,u)=>{c.notify("AnimationStart",t),o.push(In(c,t,{...r,delay:i+l(u)}).then(()=>c.notify("AnimationComplete",t)))}),Promise.all(o)}function gf(e,t){return e.sortNodePosition(t)}function yf(e,t,i={}){e.notify("AnimationStart",t);let n;if(Array.isArray(t)){const s=t.map(r=>In(e,r,i));n=Promise.all(s)}else if(typeof t=="string")n=In(e,t,i);else{const s=typeof t=="function"?ui(e,t,i.custom):t;n=Promise.all(Ya(e,s,i))}return n.then(()=>{e.notify("AnimationComplete",t)})}function Xa(e,t){if(!Array.isArray(t))return!1;const i=t.length;if(i!==e.length)return!1;for(let n=0;n<i;n++)if(t[n]!==e[n])return!1;return!0}const _f=Es.length;function $a(e){if(!e)return;if(!e.isControllingVariants){const i=e.parent?$a(e.parent)||{}:{};return e.props.initial!==void 0&&(i.initial=e.props.initial),i}const t={};for(let i=0;i<_f;i++){const n=Es[i],s=e.props[n];(ai(s)||s===!1)&&(t[n]=s)}return t}const vf=[...Rs].reverse(),xf=Rs.length;function Tf(e){return t=>Promise.all(t.map(({animation:i,options:n})=>yf(e,i,n)))}function wf(e){let t=Tf(e),i=Rr(),n=!0;const s=l=>(c,u)=>{var f;const h=ui(e,u,l==="exit"?(f=e.presenceContext)==null?void 0:f.custom:void 0);if(h){const{transition:d,transitionEnd:m,...p}=h;c={...c,...p,...m}}return c};function r(l){t=l(e)}function o(l){const{props:c}=e,u=$a(e.parent)||{},h=[],f=new Set;let d={},m=1/0;for(let g=0;g<xf;g++){const y=vf[g],x=i[y],v=c[y]!==void 0?c[y]:u[y],w=ai(v),_=y===l?x.isActive:null;_===!1&&(m=g);let T=v===u[y]&&v!==c[y]&&w;if(T&&n&&e.manuallyAnimateOnMount&&(T=!1),x.protectedKeys={...d},!x.isActive&&_===null||!v&&!x.prevProp||Gi(v)||typeof v=="boolean")continue;const b=Sf(x.prevProp,v);let S=b||y===l&&x.isActive&&!T&&w||g>m&&w,P=!1;const D=Array.isArray(v)?v:[v];let V=D.reduce(s(y),{});_===!1&&(V={});const{prevResolvedValues:k={}}=x,O={...k,...V},H=E=>{S=!0,f.has(E)&&(P=!0,f.delete(E)),x.needsAnimating[E]=!0;const X=e.getValue(E);X&&(X.liveStyle=!1)};for(const E in O){const X=V[E],Wt=k[E];if(d.hasOwnProperty(E))continue;let Ot=!1;Ln(X)&&Ln(Wt)?Ot=!Xa(X,Wt):Ot=X!==Wt,Ot?X!=null?H(E):f.add(E):X!==void 0&&f.has(E)?H(E):x.protectedKeys[E]=!0}x.prevProp=v,x.prevResolvedValues=V,x.isActive&&(d={...d,...V}),n&&e.blockInitialAnimation&&(S=!1),S&&(!(T&&b)||P)&&h.push(...D.map(E=>({animation:E,options:{type:y}})))}if(f.size){const g={};if(typeof c.initial!="boolean"){const y=ui(e,Array.isArray(c.initial)?c.initial[0]:c.initial);y&&y.transition&&(g.transition=y.transition)}f.forEach(y=>{const x=e.getBaseTarget(y),v=e.getValue(y);v&&(v.liveStyle=!0),g[y]=x??null}),h.push({animation:g})}let p=!!h.length;return n&&(c.initial===!1||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(p=!1),n=!1,p?t(h):Promise.resolve()}function a(l,c){var h;if(i[l].isActive===c)return Promise.resolve();(h=e.variantChildren)==null||h.forEach(f=>{var d;return(d=f.animationState)==null?void 0:d.setActive(l,c)}),i[l].isActive=c;const u=o(l);for(const f in i)i[f].protectedKeys={};return u}return{animateChanges:o,setActive:a,setAnimateFunction:r,getState:()=>i,reset:()=>{i=Rr(),n=!0}}}function Sf(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Xa(t,e):!1}function re(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Rr(){return{animate:re(!0),whileInView:re(),whileHover:re(),whileTap:re(),whileDrag:re(),whileFocus:re(),exit:re()}}class ee{constructor(t){this.isMounted=!1,this.node=t}update(){}}class bf extends ee{constructor(t){super(t),t.animationState||(t.animationState=wf(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Gi(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:i}=this.node.prevProps||{};t!==i&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)==null||t.call(this)}}let Pf=0;class Af extends ee{constructor(){super(...arguments),this.id=Pf++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:i}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const s=this.node.animationState.setActive("exit",!t);i&&!t&&s.then(()=>{i(this.id)})}mount(){const{register:t,onExitComplete:i}=this.node.presenceContext||{};i&&i(this.id),t&&(this.unmount=t(this.id))}unmount(){}}const Cf={animation:{Feature:bf},exit:{Feature:Af}};function ci(e,t,i,n={passive:!0}){return e.addEventListener(t,i,n),()=>e.removeEventListener(t,i)}function Ti(e){return{point:{x:e.pageX,y:e.pageY}}}const Mf=e=>t=>Ms(t)&&e(t,Ti(t));function Qe(e,t,i,n){return ci(e,t,Mf(i),n)}function Ka({top:e,left:t,right:i,bottom:n}){return{x:{min:t,max:i},y:{min:e,max:n}}}function Df({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function Rf(e,t){if(!t)return e;const i=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}const Ha=1e-4,Ef=1-Ha,Vf=1+Ha,Ga=.01,kf=0-Ga,Of=0+Ga;function ot(e){return e.max-e.min}function Lf(e,t,i){return Math.abs(e-t)<=i}function Er(e,t,i,n=.5){e.origin=n,e.originPoint=N(t.min,t.max,e.origin),e.scale=ot(i)/ot(t),e.translate=N(i.min,i.max,e.origin)-e.originPoint,(e.scale>=Ef&&e.scale<=Vf||isNaN(e.scale))&&(e.scale=1),(e.translate>=kf&&e.translate<=Of||isNaN(e.translate))&&(e.translate=0)}function Je(e,t,i,n){Er(e.x,t.x,i.x,n?n.originX:void 0),Er(e.y,t.y,i.y,n?n.originY:void 0)}function Vr(e,t,i){e.min=i.min+t.min,e.max=e.min+ot(t)}function Ff(e,t,i){Vr(e.x,t.x,i.x),Vr(e.y,t.y,i.y)}function kr(e,t,i){e.min=t.min-i.min,e.max=e.min+ot(t)}function ti(e,t,i){kr(e.x,t.x,i.x),kr(e.y,t.y,i.y)}const Or=()=>({translate:0,scale:1,origin:0,originPoint:0}),be=()=>({x:Or(),y:Or()}),Lr=()=>({min:0,max:0}),$=()=>({x:Lr(),y:Lr()});function xt(e){return[e("x"),e("y")]}function ln(e){return e===void 0||e===1}function Bn({scale:e,scaleX:t,scaleY:i}){return!ln(e)||!ln(t)||!ln(i)}function le(e){return Bn(e)||qa(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function qa(e){return Fr(e.x)||Fr(e.y)}function Fr(e){return e&&e!=="0%"}function Ni(e,t,i){const n=e-i,s=t*n;return i+s}function Ir(e,t,i,n,s){return s!==void 0&&(e=Ni(e,s,n)),Ni(e,i,n)+t}function Nn(e,t=0,i=1,n,s){e.min=Ir(e.min,t,i,n,s),e.max=Ir(e.max,t,i,n,s)}function Za(e,{x:t,y:i}){Nn(e.x,t.translate,t.scale,t.originPoint),Nn(e.y,i.translate,i.scale,i.originPoint)}const Br=.999999999999,Nr=1.0000000000001;function If(e,t,i,n=!1){const s=i.length;if(!s)return;t.x=t.y=1;let r,o;for(let a=0;a<s;a++){r=i[a],o=r.projectionDelta;const{visualElement:l}=r.options;l&&l.props.style&&l.props.style.display==="contents"||(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&Ae(e,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,Za(e,o)),n&&le(r.latestValues)&&Ae(e,r.latestValues))}t.x<Nr&&t.x>Br&&(t.x=1),t.y<Nr&&t.y>Br&&(t.y=1)}function Pe(e,t){e.min=e.min+t,e.max=e.max+t}function zr(e,t,i,n,s=.5){const r=N(e.min,e.max,s);Nn(e,t,i,r,n)}function Ae(e,t){zr(e.x,t.x,t.scaleX,t.scale,t.originX),zr(e.y,t.y,t.scaleY,t.scale,t.originY)}function Qa(e,t){return Ka(Rf(e.getBoundingClientRect(),t))}function Bf(e,t,i){const n=Qa(e,i),{scroll:s}=t;return s&&(Pe(n.x,s.offset.x),Pe(n.y,s.offset.y)),n}const Ja=({current:e})=>e?e.ownerDocument.defaultView:null,jr=(e,t)=>Math.abs(e-t);function Nf(e,t){const i=jr(e.x,t.x),n=jr(e.y,t.y);return Math.sqrt(i**2+n**2)}class tl{constructor(t,i,{transformPagePoint:n,contextWindow:s,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=cn(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,d=Nf(h.offset,{x:0,y:0})>=3;if(!f&&!d)return;const{point:m}=h,{timestamp:p}=tt;this.history.push({...m,timestamp:p});const{onStart:g,onMove:y}=this.handlers;f||(g&&g(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),y&&y(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=un(f,this.transformPagePoint),j.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:d,onSessionEnd:m,resumeAnimation:p}=this.handlers;if(this.dragSnapToOrigin&&p&&p(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const g=cn(h.type==="pointercancel"?this.lastMoveEventInfo:un(f,this.transformPagePoint),this.history);this.startEvent&&d&&d(h,g),m&&m(h,g)},!Ms(t))return;this.dragSnapToOrigin=r,this.handlers=i,this.transformPagePoint=n,this.contextWindow=s||window;const o=Ti(t),a=un(o,this.transformPagePoint),{point:l}=a,{timestamp:c}=tt;this.history=[{...l,timestamp:c}];const{onSessionStart:u}=i;u&&u(t,cn(a,this.history)),this.removeListeners=_i(Qe(this.contextWindow,"pointermove",this.handlePointerMove),Qe(this.contextWindow,"pointerup",this.handlePointerUp),Qe(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Zt(this.updatePoint)}}function un(e,t){return t?{point:t(e.point)}:e}function Ur(e,t){return{x:e.x-t.x,y:e.y-t.y}}function cn({point:e},t){return{point:e,delta:Ur(e,el(t)),offset:Ur(e,zf(t)),velocity:jf(t,.1)}}function zf(e){return e[0]}function el(e){return e[e.length-1]}function jf(e,t){if(e.length<2)return{x:0,y:0};let i=e.length-1,n=null;const s=el(e);for(;i>=0&&(n=e[i],!(s.timestamp-n.timestamp>Rt(t)));)i--;if(!n)return{x:0,y:0};const r=Et(s.timestamp-n.timestamp);if(r===0)return{x:0,y:0};const o={x:(s.x-n.x)/r,y:(s.y-n.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function Uf(e,{min:t,max:i},n){return t!==void 0&&e<t?e=n?N(t,e,n.min):Math.max(e,t):i!==void 0&&e>i&&(e=n?N(i,e,n.max):Math.min(e,i)),e}function Wr(e,t,i){return{min:t!==void 0?e.min+t:void 0,max:i!==void 0?e.max+i-(e.max-e.min):void 0}}function Wf(e,{top:t,left:i,bottom:n,right:s}){return{x:Wr(e.x,i,s),y:Wr(e.y,t,n)}}function Yr(e,t){let i=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([i,n]=[n,i]),{min:i,max:n}}function Yf(e,t){return{x:Yr(e.x,t.x),y:Yr(e.y,t.y)}}function Xf(e,t){let i=.5;const n=ot(e),s=ot(t);return s>n?i=si(t.min,t.max-n,e.min):n>s&&(i=si(e.min,e.max-s,t.min)),Nt(0,1,i)}function $f(e,t){const i={};return t.min!==void 0&&(i.min=t.min-e.min),t.max!==void 0&&(i.max=t.max-e.min),i}const zn=.35;function Kf(e=zn){return e===!1?e=0:e===!0&&(e=zn),{x:Xr(e,"left","right"),y:Xr(e,"top","bottom")}}function Xr(e,t,i){return{min:$r(e,t),max:$r(e,i)}}function $r(e,t){return typeof e=="number"?e:e[t]||0}const Hf=new WeakMap;class Gf{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=$(),this.visualElement=t}start(t,{snapToCursor:i=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&n.isPresent===!1)return;const s=u=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),i&&this.snapToCursor(Ti(u).point)},r=(u,h)=>{const{drag:f,dragPropagation:d,onDragStart:m}=this.getProps();if(f&&!d&&(this.openDragLock&&this.openDragLock(),this.openDragLock=lh(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),xt(g=>{let y=this.getAxisMotionValue(g).get()||0;if(Vt.test(y)){const{projection:x}=this.visualElement;if(x&&x.layout){const v=x.layout.layoutBox[g];v&&(y=ot(v)*(parseFloat(y)/100))}}this.originPoint[g]=y}),m&&j.postRender(()=>m(u,h)),Fn(this.visualElement,"transform");const{animationState:p}=this.visualElement;p&&p.setActive("whileDrag",!0)},o=(u,h)=>{const{dragPropagation:f,dragDirectionLock:d,onDirectionLock:m,onDrag:p}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:g}=h;if(d&&this.currentDirection===null){this.currentDirection=qf(g),this.currentDirection!==null&&m&&m(this.currentDirection);return}this.updateAxis("x",h.point,g),this.updateAxis("y",h.point,g),this.visualElement.render(),p&&p(u,h)},a=(u,h)=>this.stop(u,h),l=()=>xt(u=>{var h;return this.getAnimationState(u)==="paused"&&((h=this.getAxisMotionValue(u).animation)==null?void 0:h.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new tl(t,{onSessionStart:s,onStart:r,onMove:o,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:Ja(this.visualElement)})}stop(t,i){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:s}=i;this.startAnimation(s);const{onDragEnd:r}=this.getProps();r&&j.postRender(()=>r(t,i))}cancel(){this.isDragging=!1;const{projection:t,animationState:i}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),i&&i.setActive("whileDrag",!1)}updateAxis(t,i,n){const{drag:s}=this.getProps();if(!n||!Ai(t,s,this.currentDirection))return;const r=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=Uf(o,this.constraints[t],this.elastic[t])),r.set(o)}resolveConstraints(){var r;const{dragConstraints:t,dragElastic:i}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(r=this.visualElement.projection)==null?void 0:r.layout,s=this.constraints;t&&Se(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=Wf(n.layoutBox,t):this.constraints=!1,this.elastic=Kf(i),s!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&xt(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=$f(n.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:i}=this.getProps();if(!t||!Se(t))return!1;const n=t.current,{projection:s}=this.visualElement;if(!s||!s.layout)return!1;const r=Bf(n,s.root,this.visualElement.getTransformPagePoint());let o=Yf(s.layout.layoutBox,r);if(i){const a=i(Df(o));this.hasMutatedConstraints=!!a,a&&(o=Ka(a))}return o}startAnimation(t){const{drag:i,dragMomentum:n,dragElastic:s,dragTransition:r,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=xt(u=>{if(!Ai(u,i,this.currentDirection))return;let h=l&&l[u]||{};o&&(h={min:0,max:0});const f=s?200:1e6,d=s?40:1e7,m={type:"inertia",velocity:n?t[u]:0,bounceStiffness:f,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...r,...h};return this.startAxisValueAnimation(u,m)});return Promise.all(c).then(a)}startAxisValueAnimation(t,i){const n=this.getAxisMotionValue(t);return Fn(this.visualElement,t),n.start(Bs(t,n,0,i,this.visualElement,!1))}stopAnimation(){xt(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){xt(t=>{var i;return(i=this.getAxisMotionValue(t).animation)==null?void 0:i.pause()})}getAnimationState(t){var i;return(i=this.getAxisMotionValue(t).animation)==null?void 0:i.state}getAxisMotionValue(t){const i=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),s=n[i];return s||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){xt(i=>{const{drag:n}=this.getProps();if(!Ai(i,n,this.currentDirection))return;const{projection:s}=this.visualElement,r=this.getAxisMotionValue(i);if(s&&s.layout){const{min:o,max:a}=s.layout.layoutBox[i];r.set(t[i]-N(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:i}=this.getProps(),{projection:n}=this.visualElement;if(!Se(i)||!n||!this.constraints)return;this.stopAnimation();const s={x:0,y:0};xt(o=>{const a=this.getAxisMotionValue(o);if(a&&this.constraints!==!1){const l=a.get();s[o]=Xf({min:l,max:l},this.constraints[o])}});const{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),xt(o=>{if(!Ai(o,t,null))return;const a=this.getAxisMotionValue(o),{min:l,max:c}=this.constraints[o];a.set(N(l,c,s[o]))})}addListeners(){if(!this.visualElement.current)return;Hf.set(this.visualElement,this);const t=this.visualElement.current,i=Qe(t,"pointerdown",l=>{const{drag:c,dragListener:u=!0}=this.getProps();c&&u&&this.start(l)}),n=()=>{const{dragConstraints:l}=this.getProps();Se(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,r=s.addEventListener("measure",n);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),j.read(n);const o=ci(window,"resize",()=>this.scalePositionWithinConstraints()),a=s.addEventListener("didUpdate",({delta:l,hasLayoutChanged:c})=>{this.isDragging&&c&&(xt(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=l[u].translate,h.set(h.get()+l[u].translate))}),this.visualElement.render())});return()=>{o(),i(),r(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:i=!1,dragDirectionLock:n=!1,dragPropagation:s=!1,dragConstraints:r=!1,dragElastic:o=zn,dragMomentum:a=!0}=t;return{...t,drag:i,dragDirectionLock:n,dragPropagation:s,dragConstraints:r,dragElastic:o,dragMomentum:a}}}function Ai(e,t,i){return(t===!0||t===e)&&(i===null||i===e)}function qf(e,t=10){let i=null;return Math.abs(e.y)>t?i="y":Math.abs(e.x)>t&&(i="x"),i}class Zf extends ee{constructor(t){super(t),this.removeGroupControls=Pt,this.removeListeners=Pt,this.controls=new Gf(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Pt}unmount(){this.removeGroupControls(),this.removeListeners()}}const Kr=e=>(t,i)=>{e&&j.postRender(()=>e(t,i))};class Qf extends ee{constructor(){super(...arguments),this.removePointerDownListener=Pt}onPointerDown(t){this.session=new tl(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Ja(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:i,onPan:n,onPanEnd:s}=this.node.getProps();return{onSessionStart:Kr(t),onStart:Kr(i),onMove:n,onEnd:(r,o)=>{delete this.session,s&&j.postRender(()=>s(r,o))}}}mount(){this.removePointerDownListener=Qe(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Ei={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Hr(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const Xe={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(M.test(e))e=parseFloat(e);else return e;const i=Hr(e,t.target.x),n=Hr(e,t.target.y);return`${i}% ${n}%`}},Jf={correct:(e,{treeScale:t,projectionDelta:i})=>{const n=e,s=Qt.parse(e);if(s.length>5)return n;const r=Qt.createTransformer(e),o=typeof s[0]!="number"?1:0,a=i.x.scale*t.x,l=i.y.scale*t.y;s[0+o]/=a,s[1+o]/=l;const c=N(a,l,.5);return typeof s[2+o]=="number"&&(s[2+o]/=c),typeof s[3+o]=="number"&&(s[3+o]/=c),r(s)}};class td extends A.Component{componentDidMount(){const{visualElement:t,layoutGroup:i,switchLayoutGroup:n,layoutId:s}=this.props,{projection:r}=t;Ih(ed),r&&(i.group&&i.group.add(r),n&&n.register&&s&&n.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),Ei.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:i,visualElement:n,drag:s,isPresent:r}=this.props,{projection:o}=n;return o&&(o.isPresent=r,s||t.layoutDependency!==i||i===void 0||t.isPresent!==r?o.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?o.promote():o.relegate()||j.postRender(()=>{const a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Cs.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:i,switchLayoutGroup:n}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),i&&i.group&&i.group.remove(s),n&&n.deregister&&n.deregister(s))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function il(e){const[t,i]=Da(),n=A.useContext(ss);return Bt.jsx(td,{...e,layoutGroup:n,switchLayoutGroup:A.useContext(Oa),isPresent:t,safeToRemove:i})}const ed={borderRadius:{...Xe,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Xe,borderTopRightRadius:Xe,borderBottomLeftRadius:Xe,borderBottomRightRadius:Xe,boxShadow:Jf};function id(e,t,i){const n=nt(e)?e:Ve(e);return n.start(Bs("",n,t,i)),n.animation}const nd=(e,t)=>e.depth-t.depth;class sd{constructor(){this.children=[],this.isDirty=!1}add(t){as(this.children,t),this.isDirty=!0}remove(t){ls(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nd),this.isDirty=!1,this.children.forEach(t)}}function rd(e,t){const i=at.now(),n=({timestamp:s})=>{const r=s-i;r>=t&&(Zt(n),e(r-t))};return j.setup(n,!0),()=>Zt(n)}const nl=["TopLeft","TopRight","BottomLeft","BottomRight"],od=nl.length,Gr=e=>typeof e=="string"?parseFloat(e):e,qr=e=>typeof e=="number"||M.test(e);function ad(e,t,i,n,s,r){s?(e.opacity=N(0,i.opacity??1,ld(n)),e.opacityExit=N(t.opacity??1,0,ud(n))):r&&(e.opacity=N(t.opacity??1,i.opacity??1,n));for(let o=0;o<od;o++){const a=`border${nl[o]}Radius`;let l=Zr(t,a),c=Zr(i,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||qr(l)===qr(c)?(e[a]=Math.max(N(Gr(l),Gr(c),n),0),(Vt.test(c)||Vt.test(l))&&(e[a]+="%")):e[a]=c}(t.rotate||i.rotate)&&(e.rotate=N(t.rotate||0,i.rotate||0,n))}function Zr(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const ld=sl(0,.5,qo),ud=sl(.5,.95,Pt);function sl(e,t,i){return n=>n<e?0:n>t?1:i(si(e,t,n))}function Qr(e,t){e.min=t.min,e.max=t.max}function vt(e,t){Qr(e.x,t.x),Qr(e.y,t.y)}function Jr(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function to(e,t,i,n,s){return e-=t,e=Ni(e,1/i,n),s!==void 0&&(e=Ni(e,1/s,n)),e}function cd(e,t=0,i=1,n=.5,s,r=e,o=e){if(Vt.test(t)&&(t=parseFloat(t),t=N(o.min,o.max,t/100)-o.min),typeof t!="number")return;let a=N(r.min,r.max,n);e===r&&(a-=t),e.min=to(e.min,t,i,a,s),e.max=to(e.max,t,i,a,s)}function eo(e,t,[i,n,s],r,o){cd(e,t[i],t[n],t[s],t.scale,r,o)}const hd=["x","scaleX","originX"],fd=["y","scaleY","originY"];function io(e,t,i,n){eo(e.x,t,hd,i?i.x:void 0,n?n.x:void 0),eo(e.y,t,fd,i?i.y:void 0,n?n.y:void 0)}function no(e){return e.translate===0&&e.scale===1}function rl(e){return no(e.x)&&no(e.y)}function so(e,t){return e.min===t.min&&e.max===t.max}function dd(e,t){return so(e.x,t.x)&&so(e.y,t.y)}function ro(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function ol(e,t){return ro(e.x,t.x)&&ro(e.y,t.y)}function oo(e){return ot(e.x)/ot(e.y)}function ao(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class pd{constructor(){this.members=[]}add(t){as(this.members,t),t.scheduleRender()}remove(t){if(ls(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const i=this.members[this.members.length-1];i&&this.promote(i)}}relegate(t){const i=this.members.findIndex(s=>t===s);if(i===0)return!1;let n;for(let s=i;s>=0;s--){const r=this.members[s];if(r.isPresent!==!1){n=r;break}}return n?(this.promote(n),!0):!1}promote(t,i){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,i&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:s}=t.options;s===!1&&n.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:i,resumingFrom:n}=t;i.onExitComplete&&i.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function md(e,t,i){let n="";const s=e.x.translate/t.x,r=e.y.translate/t.y,o=(i==null?void 0:i.z)||0;if((s||r||o)&&(n=`translate3d(${s}px, ${r}px, ${o}px) `),(t.x!==1||t.y!==1)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),i){const{transformPerspective:c,rotate:u,rotateX:h,rotateY:f,skewX:d,skewY:m}=i;c&&(n=`perspective(${c}px) ${n}`),u&&(n+=`rotate(${u}deg) `),h&&(n+=`rotateX(${h}deg) `),f&&(n+=`rotateY(${f}deg) `),d&&(n+=`skewX(${d}deg) `),m&&(n+=`skewY(${m}deg) `)}const a=e.x.scale*t.x,l=e.y.scale*t.y;return(a!==1||l!==1)&&(n+=`scale(${a}, ${l})`),n||"none"}const hn=["","X","Y","Z"],gd={visibility:"hidden"},yd=1e3;let _d=0;function fn(e,t,i,n){const{latestValues:s}=t;s[e]&&(i[e]=s[e],t.setStaticValue(e,0),n&&(n[e]=0))}function al(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const i=Wa(t);if(window.MotionHasOptimisedAnimation(i,"transform")){const{layout:s,layoutId:r}=e.options;window.MotionCancelOptimisedAnimation(i,"transform",j,!(s||r))}const{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&al(n)}function ll({attachResizeListener:e,defaultParent:t,measureScroll:i,checkIsScrollRoot:n,resetTransform:s}){return class{constructor(o={},a=t==null?void 0:t()){this.id=_d++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(Td),this.nodes.forEach(Ad),this.nodes.forEach(Cd),this.nodes.forEach(wd)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new sd)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new hs),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const l=this.eventHandlers.get(o);l&&l.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o){if(this.instance)return;this.isSVG=Ma(o)&&!ph(o),this.instance=o;const{layoutId:a,layout:l,visualElement:c}=this.options;if(c&&!c.current&&c.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(l||a)&&(this.isLayoutDirty=!0),e){let u;const h=()=>this.root.updateBlockedByResize=!1;e(o,()=>{this.root.updateBlockedByResize=!0,u&&u(),u=rd(h,250),Ei.hasAnimatedSinceResize&&(Ei.hasAnimatedSinceResize=!1,this.nodes.forEach(uo))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&c&&(a||l)&&this.addEventListener("didUpdate",({delta:u,hasLayoutChanged:h,hasRelativeLayoutChanged:f,layout:d})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const m=this.options.transition||c.getDefaultTransition()||Vd,{onLayoutAnimationStart:p,onLayoutAnimationComplete:g}=c.getProps(),y=!this.targetLayout||!ol(this.targetLayout,d),x=!h&&f;if(this.options.layoutRoot||this.resumeFrom||x||h&&(y||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const v={...Ps(m,"layout"),onPlay:p,onComplete:g};(c.shouldReduceMotion||this.options.layoutRoot)&&(v.delay=0,v.type=!1),this.startAnimation(v),this.setAnimationOrigin(u,x)}else h||uo(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=d})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Zt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Md),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&al(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(lo);return}this.isUpdating||this.nodes.forEach(bd),this.isUpdating=!1,this.nodes.forEach(Pd),this.nodes.forEach(vd),this.nodes.forEach(xd),this.clearAllSnapshots();const a=at.now();tt.delta=Nt(0,1e3/60,a-tt.timestamp),tt.timestamp=a,tt.isProcessing=!0,en.update.process(tt),en.preRender.process(tt),en.render.process(tt),tt.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Cs.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Sd),this.sharedNodes.forEach(Dd)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,j.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){j.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!ot(this.snapshot.measuredBox.x)&&!ot(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=$(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a&&this.instance){const l=n(this.instance);this.scroll={animationId:this.root.animationId,phase:o,isRoot:l,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!s)return;const o=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!rl(this.projectionDelta),l=this.getTransformTemplate(),c=l?l(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;o&&this.instance&&(a||le(this.latestValues)||u)&&(s(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return o&&(l=this.removeTransform(l)),kd(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var c;const{visualElement:o}=this.options;if(!o)return $();const a=o.measureViewportBox();if(!(((c=this.scroll)==null?void 0:c.wasRoot)||this.path.some(Od))){const{scroll:u}=this.root;u&&(Pe(a.x,u.offset.x),Pe(a.y,u.offset.y))}return a}removeElementScroll(o){var l;const a=$();if(vt(a,o),(l=this.scroll)!=null&&l.wasRoot)return a;for(let c=0;c<this.path.length;c++){const u=this.path[c],{scroll:h,options:f}=u;u!==this.root&&h&&f.layoutScroll&&(h.wasRoot&&vt(a,o),Pe(a.x,h.offset.x),Pe(a.y,h.offset.y))}return a}applyTransform(o,a=!1){const l=$();vt(l,o);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&Ae(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),le(u.latestValues)&&Ae(l,u.latestValues)}return le(this.latestValues)&&Ae(l,this.latestValues),l}removeTransform(o){const a=$();vt(a,o);for(let l=0;l<this.path.length;l++){const c=this.path[l];if(!c.instance||!le(c.latestValues))continue;Bn(c.latestValues)&&c.updateSnapshot();const u=$(),h=c.measurePageBox();vt(u,h),io(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return le(this.latestValues)&&io(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==tt.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var f;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const l=!!this.resumingFrom||this!==a;if(!(o||l&&this.isSharedProjectionDirty||this.isProjectionDirty||(f=this.parent)!=null&&f.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:u,layoutId:h}=this.options;if(!(!this.layout||!(u||h))){if(this.resolvedRelativeTargetAt=tt.timestamp,!this.targetDelta&&!this.relativeTarget){const d=this.getClosestProjectingParent();d&&d.layout&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=$(),this.relativeTargetOrigin=$(),ti(this.relativeTargetOrigin,this.layout.layoutBox,d.layout.layoutBox),vt(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=$(),this.targetWithTransforms=$()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Ff(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):vt(this.target,this.layout.layoutBox),Za(this.target,this.targetDelta)):vt(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const d=this.getClosestProjectingParent();d&&!!d.resumingFrom==!!this.resumingFrom&&!d.options.layoutScroll&&d.target&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=$(),this.relativeTargetOrigin=$(),ti(this.relativeTargetOrigin,this.target,d.target),vt(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Bn(this.parent.latestValues)||qa(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var m;const o=this.getLead(),a=!!this.resumingFrom||this!==o;let l=!0;if((this.isProjectionDirty||(m=this.parent)!=null&&m.isProjectionDirty)&&(l=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(l=!1),this.resolvedRelativeTargetAt===tt.timestamp&&(l=!1),l)return;const{layout:c,layoutId:u}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||u))return;vt(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,f=this.treeScale.y;If(this.layoutCorrected,this.treeScale,this.path,a),o.layout&&!o.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(o.target=o.layout.layoutBox,o.targetWithTransforms=$());const{target:d}=o;if(!d){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Jr(this.prevProjectionDelta.x,this.projectionDelta.x),Jr(this.prevProjectionDelta.y,this.projectionDelta.y)),Je(this.projectionDelta,this.layoutCorrected,d,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==f||!ao(this.projectionDelta.x,this.prevProjectionDelta.x)||!ao(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",d))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){var a;if((a=this.options.visualElement)==null||a.scheduleRender(),o){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=be(),this.projectionDelta=be(),this.projectionDeltaWithTransform=be()}setAnimationOrigin(o,a=!1){const l=this.snapshot,c=l?l.latestValues:{},u={...this.latestValues},h=be();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=$(),d=l?l.source:void 0,m=this.layout?this.layout.source:void 0,p=d!==m,g=this.getStack(),y=!g||g.members.length<=1,x=!!(p&&!y&&this.options.crossfade===!0&&!this.path.some(Ed));this.animationProgress=0;let v;this.mixTargetDelta=w=>{const _=w/1e3;co(h.x,o.x,_),co(h.y,o.y,_),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(ti(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Rd(this.relativeTarget,this.relativeTargetOrigin,f,_),v&&dd(this.relativeTarget,v)&&(this.isProjectionDirty=!1),v||(v=$()),vt(v,this.relativeTarget)),p&&(this.animationValues=u,ad(u,c,this.latestValues,_,x,y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=_},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){var a,l,c;this.notifyListeners("animationStart"),(a=this.currentAnimation)==null||a.stop(),(c=(l=this.resumingFrom)==null?void 0:l.currentAnimation)==null||c.stop(),this.pendingAnimation&&(Zt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=j.update(()=>{Ei.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=Ve(0)),this.currentAnimation=id(this.motionValue,[0,1e3],{...o,velocity:0,isSync:!0,onUpdate:u=>{this.mixTargetDelta(u),o.onUpdate&&o.onUpdate(u)},onStop:()=>{},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(yd),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=o;if(!(!a||!l||!c)){if(this!==o&&this.layout&&c&&ul(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||$();const h=ot(this.layout.layoutBox.x);l.x.min=o.target.x.min,l.x.max=l.x.min+h;const f=ot(this.layout.layoutBox.y);l.y.min=o.target.y.min,l.y.max=l.y.min+f}vt(a,l),Ae(a,u),Je(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new pd),this.sharedNodes.get(o).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var a;const{layoutId:o}=this.options;return o?((a=this.getStack())==null?void 0:a.lead)||this:this}getPrevLead(){var a;const{layoutId:o}=this.options;return o?(a=this.getStack())==null?void 0:a.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetSkewAndRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:l}=o;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const c={};l.z&&fn("z",o,c,this.animationValues);for(let u=0;u<hn.length;u++)fn(`rotate${hn[u]}`,o,c,this.animationValues),fn(`skew${hn[u]}`,o,c,this.animationValues);o.render();for(const u in c)o.setStaticValue(u,c[u]),this.animationValues&&(this.animationValues[u]=c[u]);o.scheduleRender()}getProjectionStyles(o){if(!this.instance||this.isSVG)return;if(!this.isVisible)return gd;const a={visibility:""},l=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,a.opacity="",a.pointerEvents=Ri(o==null?void 0:o.pointerEvents)||"",a.transform=l?l(this.latestValues,""):"none",a;const c=this.getLead();if(!this.projectionDelta||!this.layout||!c.target){const d={};return this.options.layoutId&&(d.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,d.pointerEvents=Ri(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!le(this.latestValues)&&(d.transform=l?l({},""):"none",this.hasProjected=!1),d}const u=c.animationValues||c.latestValues;this.applyTransformsToTarget(),a.transform=md(this.projectionDeltaWithTransform,this.treeScale,u),l&&(a.transform=l(u,a.transform));const{x:h,y:f}=this.projectionDelta;a.transformOrigin=`${h.origin*100}% ${f.origin*100}% 0`,c.animationValues?a.opacity=c===this?u.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:u.opacityExit:a.opacity=c===this?u.opacity!==void 0?u.opacity:"":u.opacityExit!==void 0?u.opacityExit:0;for(const d in li){if(u[d]===void 0)continue;const{correct:m,applyTo:p,isCSSVariable:g}=li[d],y=a.transform==="none"?u[d]:m(u[d],c);if(p){const x=p.length;for(let v=0;v<x;v++)a[p[v]]=y}else g?this.options.visualElement.renderState.vars[d]=y:a[d]=y}return this.options.layoutId&&(a.pointerEvents=c===this?Ri(o==null?void 0:o.pointerEvents)||"":"none"),a}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)==null?void 0:a.stop()}),this.root.nodes.forEach(lo),this.root.sharedNodes.clear()}}}function vd(e){e.updateLayout()}function xd(e){var i;const t=((i=e.resumeFrom)==null?void 0:i.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:s}=e.layout,{animationType:r}=e.options,o=t.source!==e.layout.source;r==="size"?xt(h=>{const f=o?t.measuredBox[h]:t.layoutBox[h],d=ot(f);f.min=n[h].min,f.max=f.min+d}):ul(r,t.layoutBox,n)&&xt(h=>{const f=o?t.measuredBox[h]:t.layoutBox[h],d=ot(n[h]);f.max=f.min+d,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[h].max=e.relativeTarget[h].min+d)});const a=be();Je(a,n,t.layoutBox);const l=be();o?Je(l,e.applyTransform(s,!0),t.measuredBox):Je(l,n,t.layoutBox);const c=!rl(a);let u=!1;if(!e.resumeFrom){const h=e.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:d}=h;if(f&&d){const m=$();ti(m,t.layoutBox,f.layoutBox);const p=$();ti(p,n,d.layoutBox),ol(m,p)||(u=!0),h.options.layoutRoot&&(e.relativeTarget=p,e.relativeTargetOrigin=m,e.relativeParent=h)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:l,layoutDelta:a,hasLayoutChanged:c,hasRelativeLayoutChanged:u})}else if(e.isLead()){const{onExitComplete:n}=e.options;n&&n()}e.options.transition=void 0}function Td(e){e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function wd(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Sd(e){e.clearSnapshot()}function lo(e){e.clearMeasurements()}function bd(e){e.isLayoutDirty=!1}function Pd(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function uo(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Ad(e){e.resolveTargetDelta()}function Cd(e){e.calcProjection()}function Md(e){e.resetSkewAndRotation()}function Dd(e){e.removeLeadSnapshot()}function co(e,t,i){e.translate=N(t.translate,0,i),e.scale=N(t.scale,1,i),e.origin=t.origin,e.originPoint=t.originPoint}function ho(e,t,i,n){e.min=N(t.min,i.min,n),e.max=N(t.max,i.max,n)}function Rd(e,t,i,n){ho(e.x,t.x,i.x,n),ho(e.y,t.y,i.y,n)}function Ed(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const Vd={duration:.45,ease:[.4,0,.1,1]},fo=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),po=fo("applewebkit/")&&!fo("chrome/")?Math.round:Pt;function mo(e){e.min=po(e.min),e.max=po(e.max)}function kd(e){mo(e.x),mo(e.y)}function ul(e,t,i){return e==="position"||e==="preserve-aspect"&&!Lf(oo(t),oo(i),.2)}function Od(e){var t;return e!==e.root&&((t=e.scroll)==null?void 0:t.wasRoot)}const Ld=ll({attachResizeListener:(e,t)=>ci(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),dn={current:void 0},cl=ll({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!dn.current){const e=new Ld({});e.mount(window),e.setOptions({layoutScroll:!0}),dn.current=e}return dn.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),Fd={pan:{Feature:Qf},drag:{Feature:Zf,ProjectionNode:cl,MeasureLayout:il}};function go(e,t,i){const{props:n}=e;e.animationState&&n.whileHover&&e.animationState.setActive("whileHover",i==="Start");const s="onHover"+i,r=n[s];r&&j.postRender(()=>r(t,Ti(t)))}class Id extends ee{mount(){const{current:t}=this.node;t&&(this.unmount=uh(t,(i,n)=>(go(this.node,n,"Start"),s=>go(this.node,s,"End"))))}unmount(){}}class Bd extends ee{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=_i(ci(this.node.current,"focus",()=>this.onFocus()),ci(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function yo(e,t,i){const{props:n}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&n.whileTap&&e.animationState.setActive("whileTap",i==="Start");const s="onTap"+(i==="End"?"":i),r=n[s];r&&j.postRender(()=>r(t,Ti(t)))}class Nd extends ee{mount(){const{current:t}=this.node;t&&(this.unmount=dh(t,(i,n)=>(yo(this.node,n,"Start"),(s,{success:r})=>yo(this.node,s,r?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const jn=new WeakMap,pn=new WeakMap,zd=e=>{const t=jn.get(e.target);t&&t(e)},jd=e=>{e.forEach(zd)};function Ud({root:e,...t}){const i=e||document;pn.has(i)||pn.set(i,{});const n=pn.get(i),s=JSON.stringify(t);return n[s]||(n[s]=new IntersectionObserver(jd,{root:e,...t})),n[s]}function Wd(e,t,i){const n=Ud(t);return jn.set(e,i),n.observe(e),()=>{jn.delete(e),n.unobserve(e)}}const Yd={some:0,all:1};class Xd extends ee{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:i,margin:n,amount:s="some",once:r}=t,o={root:i?i.current:void 0,rootMargin:n,threshold:typeof s=="number"?s:Yd[s]},a=l=>{const{isIntersecting:c}=l;if(this.isInView===c||(this.isInView=c,r&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:u,onViewportLeave:h}=this.node.getProps(),f=c?u:h;f&&f(l)};return Wd(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:i}=this.node;["amount","margin","root"].some($d(t,i))&&this.startObserver()}unmount(){}}function $d({viewport:e={}},{viewport:t={}}={}){return i=>e[i]!==t[i]}const Kd={inView:{Feature:Xd},tap:{Feature:Nd},focus:{Feature:Bd},hover:{Feature:Id}},Hd={layout:{ProjectionNode:cl,MeasureLayout:il}},Un={current:null},hl={current:!1};function Gd(){if(hl.current=!0,!!os)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Un.current=e.matches;e.addListener(t),t()}else Un.current=!1}const qd=new WeakMap;function Zd(e,t,i){for(const n in t){const s=t[n],r=i[n];if(nt(s))e.addValue(n,s);else if(nt(r))e.addValue(n,Ve(s,{owner:e}));else if(r!==s)if(e.hasValue(n)){const o=e.getValue(n);o.liveStyle===!0?o.jump(s):o.hasAnimated||o.set(s)}else{const o=e.getStaticValue(n);e.addValue(n,Ve(o!==void 0?o:s,{owner:e}))}}for(const n in i)t[n]===void 0&&e.removeValue(n);return t}const _o=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Qd{scrapeMotionValuesFromProps(t,i,n){return{}}constructor({parent:t,props:i,presenceContext:n,reducedMotionConfig:s,blockInitialAnimation:r,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Ss,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const f=at.now();this.renderScheduledAt<f&&(this.renderScheduledAt=f,j.render(this.render,!1,!0))};const{latestValues:l,renderState:c}=o;this.latestValues=l,this.baseTarget={...l},this.initialValues=i.initial?{...l}:{},this.renderState=c,this.parent=t,this.props=i,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!r,this.isControllingVariants=qi(i),this.isVariantNode=Va(i),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(i,{},this);for(const f in h){const d=h[f];l[f]!==void 0&&nt(d)&&d.set(l[f],!1)}}mount(t){this.current=t,qd.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((i,n)=>this.bindToMotionValue(n,i)),hl.current||Gd(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Un.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Zt(this.notifyUpdate),Zt(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const i=this.features[t];i&&(i.unmount(),i.isMounted=!1)}this.current=null}bindToMotionValue(t,i){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=Ue.has(t);n&&this.onBindTransform&&this.onBindTransform();const s=i.on("change",a=>{this.latestValues[t]=a,this.props.onUpdate&&j.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),r=i.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,i)),this.valueSubscriptions.set(t,()=>{s(),r(),o&&o(),i.owner&&i.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in ke){const i=ke[t];if(!i)continue;const{isEnabled:n,Feature:s}=i;if(!this.features[t]&&s&&n(this.props)&&(this.features[t]=new s(this)),this.features[t]){const r=this.features[t];r.isMounted?r.update():(r.mount(),r.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):$()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,i){this.latestValues[t]=i}update(t,i){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=i;for(let n=0;n<_o.length;n++){const s=_o[n];this.propEventSubscriptions[s]&&(this.propEventSubscriptions[s](),delete this.propEventSubscriptions[s]);const r="on"+s,o=t[r];o&&(this.propEventSubscriptions[s]=this.on(s,o))}this.prevMotionValues=Zd(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const i=this.getClosestVariantNode();if(i)return i.variantChildren&&i.variantChildren.add(t),()=>i.variantChildren.delete(t)}addValue(t,i){const n=this.values.get(t);i!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,i),this.values.set(t,i),this.latestValues[t]=i.get())}removeValue(t){this.values.delete(t);const i=this.valueSubscriptions.get(t);i&&(i(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,i){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return n===void 0&&i!==void 0&&(n=Ve(i===null?void 0:i,{owner:this}),this.addValue(t,n)),n}readValue(t,i){let n=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options);return n!=null&&(typeof n=="string"&&(zo(n)||Uo(n))?n=parseFloat(n):!gh(n)&&Qt.test(i)&&(n=Sa(t,i)),this.setBaseTarget(t,nt(n)?n.get():n)),nt(n)?n.get():n}setBaseTarget(t,i){this.baseTarget[t]=i}getBaseTarget(t){var r;const{initial:i}=this.props;let n;if(typeof i=="string"||typeof i=="object"){const o=Fs(this.props,i,(r=this.presenceContext)==null?void 0:r.custom);o&&(n=o[t])}if(i&&n!==void 0)return n;const s=this.getBaseTargetFromProps(this.props,t);return s!==void 0&&!nt(s)?s:this.initialValues[t]!==void 0&&n===void 0?void 0:this.baseTarget[t]}on(t,i){return this.events[t]||(this.events[t]=new hs),this.events[t].add(i)}notify(t,...i){this.events[t]&&this.events[t].notify(...i)}}class fl extends Qd{constructor(){super(...arguments),this.KeyframeResolver=sh}sortInstanceNodePosition(t,i){return t.compareDocumentPosition(i)&2?1:-1}getBaseTargetFromProps(t,i){return t.style?t.style[i]:void 0}removeValueFromRenderState(t,{vars:i,style:n}){delete i[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;nt(t)&&(this.childSubscription=t.on("change",i=>{this.current&&(this.current.textContent=`${i}`)}))}}function dl(e,{style:t,vars:i},n,s){Object.assign(e.style,t,s&&s.getProjectionStyles(n));for(const r in i)e.style.setProperty(r,i[r])}function Jd(e){return window.getComputedStyle(e)}class tp extends fl{constructor(){super(...arguments),this.type="html",this.renderInstance=dl}readValueFromInstance(t,i){var n;if(Ue.has(i))return(n=this.projection)!=null&&n.isProjecting?Dn(i):Sc(t,i);{const s=Jd(t),r=(ps(i)?s.getPropertyValue(i):s[i])||0;return typeof r=="string"?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:i}){return Qa(t,i)}build(t,i,n){ks(t,i,n.transformTemplate)}scrapeMotionValuesFromProps(t,i,n){return Is(t,i,n)}}const pl=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function ep(e,t,i,n){dl(e,t,void 0,n);for(const s in t.attrs)e.setAttribute(pl.has(s)?s:Vs(s),t.attrs[s])}class ip extends fl{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=$}getBaseTargetFromProps(t,i){return t[i]}readValueFromInstance(t,i){if(Ue.has(i)){const n=wa(i);return n&&n.default||0}return i=pl.has(i)?i:Vs(i),t.getAttribute(i)}scrapeMotionValuesFromProps(t,i,n){return Ua(t,i,n)}build(t,i,n){Ba(t,i,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,i,n,s){ep(t,i,n,s)}mount(t){this.isSVGTag=za(t.tagName),super.mount(t)}}const np=(e,t)=>Ls(e)?new ip(t):new tp(t,{allowProjection:e!==A.Fragment}),sp=tf({...Cf,...Kd,...Fd,...Hd},np),km=Ph(sp);function Ft(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ml(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}/*!
 * GSAP 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var gt={autoSleep:120,force3D:"auto",nullTargetWarn:1,units:{lineHeight:""}},Oe={duration:.5,overwrite:!1,delay:0},Ns,et,I,wt=1e8,F=1/wt,Wn=Math.PI*2,rp=Wn/4,op=0,gl=Math.sqrt,ap=Math.cos,lp=Math.sin,J=function(t){return typeof t=="string"},W=function(t){return typeof t=="function"},jt=function(t){return typeof t=="number"},zs=function(t){return typeof t>"u"},kt=function(t){return typeof t=="object"},lt=function(t){return t!==!1},js=function(){return typeof window<"u"},Ci=function(t){return W(t)||J(t)},yl=typeof ArrayBuffer=="function"&&ArrayBuffer.isView||function(){},st=Array.isArray,Yn=/(?:-?\.?\d|\.)+/gi,_l=/[-+=.]*\d+[.e\-+]*\d*[e\-+]*\d*/g,Ce=/[-+=.]*\d+[.e-]*\d*[a-z%]*/g,mn=/[-+=.]*\d+\.?\d*(?:e-|e\+)?\d*/gi,vl=/[+-]=-?[.\d]+/,xl=/[^,'"\[\]\s]+/gi,up=/^[+\-=e\s\d]*\d+[.\d]*([a-z]*|%)\s*$/i,B,Ct,Xn,Us,yt={},zi={},Tl,wl=function(t){return(zi=Le(t,yt))&&ft},Ws=function(t,i){return console.warn("Invalid property",t,"set to",i,"Missing plugin? gsap.registerPlugin()")},hi=function(t,i){return!i&&console.warn(t)},Sl=function(t,i){return t&&(yt[t]=i)&&zi&&(zi[t]=i)||yt},fi=function(){return 0},cp={suppressEvents:!0,isStart:!0,kill:!1},Vi={suppressEvents:!0,kill:!1},hp={suppressEvents:!0},Ys={},Gt=[],$n={},bl,dt={},gn={},vo=30,ki=[],Xs="",$s=function(t){var i=t[0],n,s;if(kt(i)||W(i)||(t=[t]),!(n=(i._gsap||{}).harness)){for(s=ki.length;s--&&!ki[s].targetTest(i););n=ki[s]}for(s=t.length;s--;)t[s]&&(t[s]._gsap||(t[s]._gsap=new Hl(t[s],n)))||t.splice(s,1);return t},me=function(t){return t._gsap||$s(St(t))[0]._gsap},Pl=function(t,i,n){return(n=t[i])&&W(n)?t[i]():zs(n)&&t.getAttribute&&t.getAttribute(i)||n},ut=function(t,i){return(t=t.split(",")).forEach(i)||t},K=function(t){return Math.round(t*1e5)/1e5||0},Z=function(t){return Math.round(t*1e7)/1e7||0},De=function(t,i){var n=i.charAt(0),s=parseFloat(i.substr(2));return t=parseFloat(t),n==="+"?t+s:n==="-"?t-s:n==="*"?t*s:t/s},fp=function(t,i){for(var n=i.length,s=0;t.indexOf(i[s])<0&&++s<n;);return s<n},ji=function(){var t=Gt.length,i=Gt.slice(0),n,s;for($n={},Gt.length=0,n=0;n<t;n++)s=i[n],s&&s._lazy&&(s.render(s._lazy[0],s._lazy[1],!0)._lazy=0)},Ks=function(t){return!!(t._initted||t._startAt||t.add)},Al=function(t,i,n,s){Gt.length&&!et&&ji(),t.render(i,n,!!(et&&i<0&&Ks(t))),Gt.length&&!et&&ji()},Cl=function(t){var i=parseFloat(t);return(i||i===0)&&(t+"").match(xl).length<2?i:J(t)?t.trim():t},Ml=function(t){return t},_t=function(t,i){for(var n in i)n in t||(t[n]=i[n]);return t},dp=function(t){return function(i,n){for(var s in n)s in i||s==="duration"&&t||s==="ease"||(i[s]=n[s])}},Le=function(t,i){for(var n in i)t[n]=i[n];return t},xo=function e(t,i){for(var n in i)n!=="__proto__"&&n!=="constructor"&&n!=="prototype"&&(t[n]=kt(i[n])?e(t[n]||(t[n]={}),i[n]):i[n]);return t},Ui=function(t,i){var n={},s;for(s in t)s in i||(n[s]=t[s]);return n},ei=function(t){var i=t.parent||B,n=t.keyframes?dp(st(t.keyframes)):_t;if(lt(t.inherit))for(;i;)n(t,i.vars.defaults),i=i.parent||i._dp;return t},pp=function(t,i){for(var n=t.length,s=n===i.length;s&&n--&&t[n]===i[n];);return n<0},Dl=function(t,i,n,s,r){var o=t[s],a;if(r)for(a=i[r];o&&o[r]>a;)o=o._prev;return o?(i._next=o._next,o._next=i):(i._next=t[n],t[n]=i),i._next?i._next._prev=i:t[s]=i,i._prev=o,i.parent=i._dp=t,i},Zi=function(t,i,n,s){n===void 0&&(n="_first"),s===void 0&&(s="_last");var r=i._prev,o=i._next;r?r._next=o:t[n]===i&&(t[n]=o),o?o._prev=r:t[s]===i&&(t[s]=r),i._next=i._prev=i.parent=null},Jt=function(t,i){t.parent&&(!i||t.parent.autoRemoveChildren)&&t.parent.remove&&t.parent.remove(t),t._act=0},ge=function(t,i){if(t&&(!i||i._end>t._dur||i._start<0))for(var n=t;n;)n._dirty=1,n=n.parent;return t},mp=function(t){for(var i=t.parent;i&&i.parent;)i._dirty=1,i.totalDuration(),i=i.parent;return t},Kn=function(t,i,n,s){return t._startAt&&(et?t._startAt.revert(Vi):t.vars.immediateRender&&!t.vars.autoRevert||t._startAt.render(i,!0,s))},gp=function e(t){return!t||t._ts&&e(t.parent)},To=function(t){return t._repeat?Fe(t._tTime,t=t.duration()+t._rDelay)*t:0},Fe=function(t,i){var n=Math.floor(t=Z(t/i));return t&&n===t?n-1:n},Wi=function(t,i){return(t-i._start)*i._ts+(i._ts>=0?0:i._dirty?i.totalDuration():i._tDur)},Qi=function(t){return t._end=Z(t._start+(t._tDur/Math.abs(t._ts||t._rts||F)||0))},Ji=function(t,i){var n=t._dp;return n&&n.smoothChildTiming&&t._ts&&(t._start=Z(n._time-(t._ts>0?i/t._ts:((t._dirty?t.totalDuration():t._tDur)-i)/-t._ts)),Qi(t),n._dirty||ge(n,t)),t},Rl=function(t,i){var n;if((i._time||!i._dur&&i._initted||i._start<t._time&&(i._dur||!i.add))&&(n=Wi(t.rawTime(),i),(!i._dur||wi(0,i.totalDuration(),n)-i._tTime>F)&&i.render(n,!0)),ge(t,i)._dp&&t._initted&&t._time>=t._dur&&t._ts){if(t._dur<t.duration())for(n=t;n._dp;)n.rawTime()>=0&&n.totalTime(n._tTime),n=n._dp;t._zTime=-F}},Mt=function(t,i,n,s){return i.parent&&Jt(i),i._start=Z((jt(n)?n:n||t!==B?Tt(t,n,i):t._time)+i._delay),i._end=Z(i._start+(i.totalDuration()/Math.abs(i.timeScale())||0)),Dl(t,i,"_first","_last",t._sort?"_start":0),Hn(i)||(t._recent=i),s||Rl(t,i),t._ts<0&&Ji(t,t._tTime),t},El=function(t,i){return(yt.ScrollTrigger||Ws("scrollTrigger",i))&&yt.ScrollTrigger.create(i,t)},Vl=function(t,i,n,s,r){if(Gs(t,i,r),!t._initted)return 1;if(!n&&t._pt&&!et&&(t._dur&&t.vars.lazy!==!1||!t._dur&&t.vars.lazy)&&bl!==pt.frame)return Gt.push(t),t._lazy=[r,s],1},yp=function e(t){var i=t.parent;return i&&i._ts&&i._initted&&!i._lock&&(i.rawTime()<0||e(i))},Hn=function(t){var i=t.data;return i==="isFromStart"||i==="isStart"},_p=function(t,i,n,s){var r=t.ratio,o=i<0||!i&&(!t._start&&yp(t)&&!(!t._initted&&Hn(t))||(t._ts<0||t._dp._ts<0)&&!Hn(t))?0:1,a=t._rDelay,l=0,c,u,h;if(a&&t._repeat&&(l=wi(0,t._tDur,i),u=Fe(l,a),t._yoyo&&u&1&&(o=1-o),u!==Fe(t._tTime,a)&&(r=1-o,t.vars.repeatRefresh&&t._initted&&t.invalidate())),o!==r||et||s||t._zTime===F||!i&&t._zTime){if(!t._initted&&Vl(t,i,s,n,l))return;for(h=t._zTime,t._zTime=i||(n?F:0),n||(n=i&&!h),t.ratio=o,t._from&&(o=1-o),t._time=0,t._tTime=l,c=t._pt;c;)c.r(o,c.d),c=c._next;i<0&&Kn(t,i,n,!0),t._onUpdate&&!n&&mt(t,"onUpdate"),l&&t._repeat&&!n&&t.parent&&mt(t,"onRepeat"),(i>=t._tDur||i<0)&&t.ratio===o&&(o&&Jt(t,1),!n&&!et&&(mt(t,o?"onComplete":"onReverseComplete",!0),t._prom&&t._prom()))}else t._zTime||(t._zTime=i)},vp=function(t,i,n){var s;if(n>i)for(s=t._first;s&&s._start<=n;){if(s.data==="isPause"&&s._start>i)return s;s=s._next}else for(s=t._last;s&&s._start>=n;){if(s.data==="isPause"&&s._start<i)return s;s=s._prev}},Ie=function(t,i,n,s){var r=t._repeat,o=Z(i)||0,a=t._tTime/t._tDur;return a&&!s&&(t._time*=o/t._dur),t._dur=o,t._tDur=r?r<0?1e10:Z(o*(r+1)+t._rDelay*r):o,a>0&&!s&&Ji(t,t._tTime=t._tDur*a),t.parent&&Qi(t),n||ge(t.parent,t),t},wo=function(t){return t instanceof rt?ge(t):Ie(t,t._dur)},xp={_start:0,endTime:fi,totalDuration:fi},Tt=function e(t,i,n){var s=t.labels,r=t._recent||xp,o=t.duration()>=wt?r.endTime(!1):t._dur,a,l,c;return J(i)&&(isNaN(i)||i in s)?(l=i.charAt(0),c=i.substr(-1)==="%",a=i.indexOf("="),l==="<"||l===">"?(a>=0&&(i=i.replace(/=/,"")),(l==="<"?r._start:r.endTime(r._repeat>=0))+(parseFloat(i.substr(1))||0)*(c?(a<0?r:n).totalDuration()/100:1)):a<0?(i in s||(s[i]=o),s[i]):(l=parseFloat(i.charAt(a-1)+i.substr(a+1)),c&&n&&(l=l/100*(st(n)?n[0]:n).totalDuration()),a>1?e(t,i.substr(0,a-1),n)+l:o+l)):i==null?o:+i},ii=function(t,i,n){var s=jt(i[1]),r=(s?2:1)+(t<2?0:1),o=i[r],a,l;if(s&&(o.duration=i[1]),o.parent=n,t){for(a=o,l=n;l&&!("immediateRender"in a);)a=l.vars.defaults||{},l=lt(l.vars.inherit)&&l.parent;o.immediateRender=lt(a.immediateRender),t<2?o.runBackwards=1:o.startAt=i[r-1]}return new q(i[0],o,i[r+1])},ie=function(t,i){return t||t===0?i(t):i},wi=function(t,i,n){return n<t?t:n>i?i:n},it=function(t,i){return!J(t)||!(i=up.exec(t))?"":i[1]},Tp=function(t,i,n){return ie(n,function(s){return wi(t,i,s)})},Gn=[].slice,kl=function(t,i){return t&&kt(t)&&"length"in t&&(!i&&!t.length||t.length-1 in t&&kt(t[0]))&&!t.nodeType&&t!==Ct},wp=function(t,i,n){return n===void 0&&(n=[]),t.forEach(function(s){var r;return J(s)&&!i||kl(s,1)?(r=n).push.apply(r,St(s)):n.push(s)})||n},St=function(t,i,n){return I&&!i&&I.selector?I.selector(t):J(t)&&!n&&(Xn||!Be())?Gn.call((i||Us).querySelectorAll(t),0):st(t)?wp(t,n):kl(t)?Gn.call(t,0):t?[t]:[]},qn=function(t){return t=St(t)[0]||hi("Invalid scope")||{},function(i){var n=t.current||t.nativeElement||t;return St(i,n.querySelectorAll?n:n===t?hi("Invalid scope")||Us.createElement("div"):t)}},Ol=function(t){return t.sort(function(){return .5-Math.random()})},Ll=function(t){if(W(t))return t;var i=kt(t)?t:{each:t},n=ye(i.ease),s=i.from||0,r=parseFloat(i.base)||0,o={},a=s>0&&s<1,l=isNaN(s)||a,c=i.axis,u=s,h=s;return J(s)?u=h={center:.5,edges:.5,end:1}[s]||0:!a&&l&&(u=s[0],h=s[1]),function(f,d,m){var p=(m||i).length,g=o[p],y,x,v,w,_,T,b,S,P;if(!g){if(P=i.grid==="auto"?0:(i.grid||[1,wt])[1],!P){for(b=-wt;b<(b=m[P++].getBoundingClientRect().left)&&P<p;);P<p&&P--}for(g=o[p]=[],y=l?Math.min(P,p)*u-.5:s%P,x=P===wt?0:l?p*h/P-.5:s/P|0,b=0,S=wt,T=0;T<p;T++)v=T%P-y,w=x-(T/P|0),g[T]=_=c?Math.abs(c==="y"?w:v):gl(v*v+w*w),_>b&&(b=_),_<S&&(S=_);s==="random"&&Ol(g),g.max=b-S,g.min=S,g.v=p=(parseFloat(i.amount)||parseFloat(i.each)*(P>p?p-1:c?c==="y"?p/P:P:Math.max(P,p/P))||0)*(s==="edges"?-1:1),g.b=p<0?r-p:r,g.u=it(i.amount||i.each)||0,n=n&&p<0?Xl(n):n}return p=(g[f]-g.min)/g.max||0,Z(g.b+(n?n(p):p)*g.v)+g.u}},Zn=function(t){var i=Math.pow(10,((t+"").split(".")[1]||"").length);return function(n){var s=Z(Math.round(parseFloat(n)/t)*t*i);return(s-s%1)/i+(jt(n)?0:it(n))}},Fl=function(t,i){var n=st(t),s,r;return!n&&kt(t)&&(s=n=t.radius||wt,t.values?(t=St(t.values),(r=!jt(t[0]))&&(s*=s)):t=Zn(t.increment)),ie(i,n?W(t)?function(o){return r=t(o),Math.abs(r-o)<=s?r:o}:function(o){for(var a=parseFloat(r?o.x:o),l=parseFloat(r?o.y:0),c=wt,u=0,h=t.length,f,d;h--;)r?(f=t[h].x-a,d=t[h].y-l,f=f*f+d*d):f=Math.abs(t[h]-a),f<c&&(c=f,u=h);return u=!s||c<=s?t[u]:o,r||u===o||jt(o)?u:u+it(o)}:Zn(t))},Il=function(t,i,n,s){return ie(st(t)?!i:n===!0?!!(n=0):!s,function(){return st(t)?t[~~(Math.random()*t.length)]:(n=n||1e-5)&&(s=n<1?Math.pow(10,(n+"").length-2):1)&&Math.floor(Math.round((t-n/2+Math.random()*(i-t+n*.99))/n)*n*s)/s})},Sp=function(){for(var t=arguments.length,i=new Array(t),n=0;n<t;n++)i[n]=arguments[n];return function(s){return i.reduce(function(r,o){return o(r)},s)}},bp=function(t,i){return function(n){return t(parseFloat(n))+(i||it(n))}},Pp=function(t,i,n){return Nl(t,i,0,1,n)},Bl=function(t,i,n){return ie(n,function(s){return t[~~i(s)]})},Ap=function e(t,i,n){var s=i-t;return st(t)?Bl(t,e(0,t.length),i):ie(n,function(r){return(s+(r-t)%s)%s+t})},Cp=function e(t,i,n){var s=i-t,r=s*2;return st(t)?Bl(t,e(0,t.length-1),i):ie(n,function(o){return o=(r+(o-t)%r)%r||0,t+(o>s?r-o:o)})},di=function(t){for(var i=0,n="",s,r,o,a;~(s=t.indexOf("random(",i));)o=t.indexOf(")",s),a=t.charAt(s+7)==="[",r=t.substr(s+7,o-s-7).match(a?xl:Yn),n+=t.substr(i,s-i)+Il(a?r:+r[0],a?0:+r[1],+r[2]||1e-5),i=o+1;return n+t.substr(i,t.length-i)},Nl=function(t,i,n,s,r){var o=i-t,a=s-n;return ie(r,function(l){return n+((l-t)/o*a||0)})},Mp=function e(t,i,n,s){var r=isNaN(t+i)?0:function(d){return(1-d)*t+d*i};if(!r){var o=J(t),a={},l,c,u,h,f;if(n===!0&&(s=1)&&(n=null),o)t={p:t},i={p:i};else if(st(t)&&!st(i)){for(u=[],h=t.length,f=h-2,c=1;c<h;c++)u.push(e(t[c-1],t[c]));h--,r=function(m){m*=h;var p=Math.min(f,~~m);return u[p](m-p)},n=i}else s||(t=Le(st(t)?[]:{},t));if(!u){for(l in i)Hs.call(a,t,l,"get",i[l]);r=function(m){return Qs(m,a)||(o?t.p:t)}}}return ie(n,r)},So=function(t,i,n){var s=t.labels,r=wt,o,a,l;for(o in s)a=s[o]-i,a<0==!!n&&a&&r>(a=Math.abs(a))&&(l=o,r=a);return l},mt=function(t,i,n){var s=t.vars,r=s[i],o=I,a=t._ctx,l,c,u;if(r)return l=s[i+"Params"],c=s.callbackScope||t,n&&Gt.length&&ji(),a&&(I=a),u=l?r.apply(c,l):r.call(c),I=o,u},He=function(t){return Jt(t),t.scrollTrigger&&t.scrollTrigger.kill(!!et),t.progress()<1&&mt(t,"onInterrupt"),t},Me,zl=[],jl=function(t){if(t)if(t=!t.name&&t.default||t,js()||t.headless){var i=t.name,n=W(t),s=i&&!n&&t.init?function(){this._props=[]}:t,r={init:fi,render:Qs,add:Hs,kill:Yp,modifier:Wp,rawVars:0},o={targetTest:0,get:0,getSetter:Zs,aliases:{},register:0};if(Be(),t!==s){if(dt[i])return;_t(s,_t(Ui(t,r),o)),Le(s.prototype,Le(r,Ui(t,o))),dt[s.prop=i]=s,t.targetTest&&(ki.push(s),Ys[i]=1),i=(i==="css"?"CSS":i.charAt(0).toUpperCase()+i.substr(1))+"Plugin"}Sl(i,s),t.register&&t.register(ft,s,ct)}else zl.push(t)},L=255,Ge={aqua:[0,L,L],lime:[0,L,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,L],navy:[0,0,128],white:[L,L,L],olive:[128,128,0],yellow:[L,L,0],orange:[L,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[L,0,0],pink:[L,192,203],cyan:[0,L,L],transparent:[L,L,L,0]},yn=function(t,i,n){return t+=t<0?1:t>1?-1:0,(t*6<1?i+(n-i)*t*6:t<.5?n:t*3<2?i+(n-i)*(2/3-t)*6:i)*L+.5|0},Ul=function(t,i,n){var s=t?jt(t)?[t>>16,t>>8&L,t&L]:0:Ge.black,r,o,a,l,c,u,h,f,d,m;if(!s){if(t.substr(-1)===","&&(t=t.substr(0,t.length-1)),Ge[t])s=Ge[t];else if(t.charAt(0)==="#"){if(t.length<6&&(r=t.charAt(1),o=t.charAt(2),a=t.charAt(3),t="#"+r+r+o+o+a+a+(t.length===5?t.charAt(4)+t.charAt(4):"")),t.length===9)return s=parseInt(t.substr(1,6),16),[s>>16,s>>8&L,s&L,parseInt(t.substr(7),16)/255];t=parseInt(t.substr(1),16),s=[t>>16,t>>8&L,t&L]}else if(t.substr(0,3)==="hsl"){if(s=m=t.match(Yn),!i)l=+s[0]%360/360,c=+s[1]/100,u=+s[2]/100,o=u<=.5?u*(c+1):u+c-u*c,r=u*2-o,s.length>3&&(s[3]*=1),s[0]=yn(l+1/3,r,o),s[1]=yn(l,r,o),s[2]=yn(l-1/3,r,o);else if(~t.indexOf("="))return s=t.match(_l),n&&s.length<4&&(s[3]=1),s}else s=t.match(Yn)||Ge.transparent;s=s.map(Number)}return i&&!m&&(r=s[0]/L,o=s[1]/L,a=s[2]/L,h=Math.max(r,o,a),f=Math.min(r,o,a),u=(h+f)/2,h===f?l=c=0:(d=h-f,c=u>.5?d/(2-h-f):d/(h+f),l=h===r?(o-a)/d+(o<a?6:0):h===o?(a-r)/d+2:(r-o)/d+4,l*=60),s[0]=~~(l+.5),s[1]=~~(c*100+.5),s[2]=~~(u*100+.5)),n&&s.length<4&&(s[3]=1),s},Wl=function(t){var i=[],n=[],s=-1;return t.split(qt).forEach(function(r){var o=r.match(Ce)||[];i.push.apply(i,o),n.push(s+=o.length+1)}),i.c=n,i},bo=function(t,i,n){var s="",r=(t+s).match(qt),o=i?"hsla(":"rgba(",a=0,l,c,u,h;if(!r)return t;if(r=r.map(function(f){return(f=Ul(f,i,1))&&o+(i?f[0]+","+f[1]+"%,"+f[2]+"%,"+f[3]:f.join(","))+")"}),n&&(u=Wl(t),l=n.c,l.join(s)!==u.c.join(s)))for(c=t.replace(qt,"1").split(Ce),h=c.length-1;a<h;a++)s+=c[a]+(~l.indexOf(a)?r.shift()||o+"0,0,0,0)":(u.length?u:r.length?r:n).shift());if(!c)for(c=t.split(qt),h=c.length-1;a<h;a++)s+=c[a]+r[a];return s+c[h]},qt=function(){var e="(?:\\b(?:(?:rgb|rgba|hsl|hsla)\\(.+?\\))|\\B#(?:[0-9a-f]{3,4}){1,2}\\b",t;for(t in Ge)e+="|"+t+"\\b";return new RegExp(e+")","gi")}(),Dp=/hsl[a]?\(/,Yl=function(t){var i=t.join(" "),n;if(qt.lastIndex=0,qt.test(i))return n=Dp.test(i),t[1]=bo(t[1],n),t[0]=bo(t[0],n,Wl(t[1])),!0},pi,pt=function(){var e=Date.now,t=500,i=33,n=e(),s=n,r=1e3/240,o=r,a=[],l,c,u,h,f,d,m=function p(g){var y=e()-s,x=g===!0,v,w,_,T;if((y>t||y<0)&&(n+=y-i),s+=y,_=s-n,v=_-o,(v>0||x)&&(T=++h.frame,f=_-h.time*1e3,h.time=_=_/1e3,o+=v+(v>=r?4:r-v),w=1),x||(l=c(p)),w)for(d=0;d<a.length;d++)a[d](_,f,T,g)};return h={time:0,frame:0,tick:function(){m(!0)},deltaRatio:function(g){return f/(1e3/(g||60))},wake:function(){Tl&&(!Xn&&js()&&(Ct=Xn=window,Us=Ct.document||{},yt.gsap=ft,(Ct.gsapVersions||(Ct.gsapVersions=[])).push(ft.version),wl(zi||Ct.GreenSockGlobals||!Ct.gsap&&Ct||{}),zl.forEach(jl)),u=typeof requestAnimationFrame<"u"&&requestAnimationFrame,l&&h.sleep(),c=u||function(g){return setTimeout(g,o-h.time*1e3+1|0)},pi=1,m(2))},sleep:function(){(u?cancelAnimationFrame:clearTimeout)(l),pi=0,c=fi},lagSmoothing:function(g,y){t=g||1/0,i=Math.min(y||33,t)},fps:function(g){r=1e3/(g||240),o=h.time*1e3+r},add:function(g,y,x){var v=y?function(w,_,T,b){g(w,_,T,b),h.remove(v)}:g;return h.remove(g),a[x?"unshift":"push"](v),Be(),v},remove:function(g,y){~(y=a.indexOf(g))&&a.splice(y,1)&&d>=y&&d--},_listeners:a},h}(),Be=function(){return!pi&&pt.wake()},R={},Rp=/^[\d.\-M][\d.\-,\s]/,Ep=/["']/g,Vp=function(t){for(var i={},n=t.substr(1,t.length-3).split(":"),s=n[0],r=1,o=n.length,a,l,c;r<o;r++)l=n[r],a=r!==o-1?l.lastIndexOf(","):l.length,c=l.substr(0,a),i[s]=isNaN(c)?c.replace(Ep,"").trim():+c,s=l.substr(a+1).trim();return i},kp=function(t){var i=t.indexOf("(")+1,n=t.indexOf(")"),s=t.indexOf("(",i);return t.substring(i,~s&&s<n?t.indexOf(")",n+1):n)},Op=function(t){var i=(t+"").split("("),n=R[i[0]];return n&&i.length>1&&n.config?n.config.apply(null,~t.indexOf("{")?[Vp(i[1])]:kp(t).split(",").map(Cl)):R._CE&&Rp.test(t)?R._CE("",t):n},Xl=function(t){return function(i){return 1-t(1-i)}},$l=function e(t,i){for(var n=t._first,s;n;)n instanceof rt?e(n,i):n.vars.yoyoEase&&(!n._yoyo||!n._repeat)&&n._yoyo!==i&&(n.timeline?e(n.timeline,i):(s=n._ease,n._ease=n._yEase,n._yEase=s,n._yoyo=i)),n=n._next},ye=function(t,i){return t&&(W(t)?t:R[t]||Op(t))||i},xe=function(t,i,n,s){n===void 0&&(n=function(l){return 1-i(1-l)}),s===void 0&&(s=function(l){return l<.5?i(l*2)/2:1-i((1-l)*2)/2});var r={easeIn:i,easeOut:n,easeInOut:s},o;return ut(t,function(a){R[a]=yt[a]=r,R[o=a.toLowerCase()]=n;for(var l in r)R[o+(l==="easeIn"?".in":l==="easeOut"?".out":".inOut")]=R[a+"."+l]=r[l]}),r},Kl=function(t){return function(i){return i<.5?(1-t(1-i*2))/2:.5+t((i-.5)*2)/2}},_n=function e(t,i,n){var s=i>=1?i:1,r=(n||(t?.3:.45))/(i<1?i:1),o=r/Wn*(Math.asin(1/s)||0),a=function(u){return u===1?1:s*Math.pow(2,-10*u)*lp((u-o)*r)+1},l=t==="out"?a:t==="in"?function(c){return 1-a(1-c)}:Kl(a);return r=Wn/r,l.config=function(c,u){return e(t,c,u)},l},vn=function e(t,i){i===void 0&&(i=1.70158);var n=function(o){return o?--o*o*((i+1)*o+i)+1:0},s=t==="out"?n:t==="in"?function(r){return 1-n(1-r)}:Kl(n);return s.config=function(r){return e(t,r)},s};ut("Linear,Quad,Cubic,Quart,Quint,Strong",function(e,t){var i=t<5?t+1:t;xe(e+",Power"+(i-1),t?function(n){return Math.pow(n,i)}:function(n){return n},function(n){return 1-Math.pow(1-n,i)},function(n){return n<.5?Math.pow(n*2,i)/2:1-Math.pow((1-n)*2,i)/2})});R.Linear.easeNone=R.none=R.Linear.easeIn;xe("Elastic",_n("in"),_n("out"),_n());(function(e,t){var i=1/t,n=2*i,s=2.5*i,r=function(a){return a<i?e*a*a:a<n?e*Math.pow(a-1.5/t,2)+.75:a<s?e*(a-=2.25/t)*a+.9375:e*Math.pow(a-2.625/t,2)+.984375};xe("Bounce",function(o){return 1-r(1-o)},r)})(7.5625,2.75);xe("Expo",function(e){return Math.pow(2,10*(e-1))*e+e*e*e*e*e*e*(1-e)});xe("Circ",function(e){return-(gl(1-e*e)-1)});xe("Sine",function(e){return e===1?1:-ap(e*rp)+1});xe("Back",vn("in"),vn("out"),vn());R.SteppedEase=R.steps=yt.SteppedEase={config:function(t,i){t===void 0&&(t=1);var n=1/t,s=t+(i?0:1),r=i?1:0,o=1-F;return function(a){return((s*wi(0,o,a)|0)+r)*n}}};Oe.ease=R["quad.out"];ut("onComplete,onUpdate,onStart,onRepeat,onReverseComplete,onInterrupt",function(e){return Xs+=e+","+e+"Params,"});var Hl=function(t,i){this.id=op++,t._gsap=this,this.target=t,this.harness=i,this.get=i?i.get:Pl,this.set=i?i.getSetter:Zs},mi=function(){function e(i){this.vars=i,this._delay=+i.delay||0,(this._repeat=i.repeat===1/0?-2:i.repeat||0)&&(this._rDelay=i.repeatDelay||0,this._yoyo=!!i.yoyo||!!i.yoyoEase),this._ts=1,Ie(this,+i.duration,1,1),this.data=i.data,I&&(this._ctx=I,I.data.push(this)),pi||pt.wake()}var t=e.prototype;return t.delay=function(n){return n||n===0?(this.parent&&this.parent.smoothChildTiming&&this.startTime(this._start+n-this._delay),this._delay=n,this):this._delay},t.duration=function(n){return arguments.length?this.totalDuration(this._repeat>0?n+(n+this._rDelay)*this._repeat:n):this.totalDuration()&&this._dur},t.totalDuration=function(n){return arguments.length?(this._dirty=0,Ie(this,this._repeat<0?n:(n-this._repeat*this._rDelay)/(this._repeat+1))):this._tDur},t.totalTime=function(n,s){if(Be(),!arguments.length)return this._tTime;var r=this._dp;if(r&&r.smoothChildTiming&&this._ts){for(Ji(this,n),!r._dp||r.parent||Rl(r,this);r&&r.parent;)r.parent._time!==r._start+(r._ts>=0?r._tTime/r._ts:(r.totalDuration()-r._tTime)/-r._ts)&&r.totalTime(r._tTime,!0),r=r.parent;!this.parent&&this._dp.autoRemoveChildren&&(this._ts>0&&n<this._tDur||this._ts<0&&n>0||!this._tDur&&!n)&&Mt(this._dp,this,this._start-this._delay)}return(this._tTime!==n||!this._dur&&!s||this._initted&&Math.abs(this._zTime)===F||!n&&!this._initted&&(this.add||this._ptLookup))&&(this._ts||(this._pTime=n),Al(this,n,s)),this},t.time=function(n,s){return arguments.length?this.totalTime(Math.min(this.totalDuration(),n+To(this))%(this._dur+this._rDelay)||(n?this._dur:0),s):this._time},t.totalProgress=function(n,s){return arguments.length?this.totalTime(this.totalDuration()*n,s):this.totalDuration()?Math.min(1,this._tTime/this._tDur):this.rawTime()>=0&&this._initted?1:0},t.progress=function(n,s){return arguments.length?this.totalTime(this.duration()*(this._yoyo&&!(this.iteration()&1)?1-n:n)+To(this),s):this.duration()?Math.min(1,this._time/this._dur):this.rawTime()>0?1:0},t.iteration=function(n,s){var r=this.duration()+this._rDelay;return arguments.length?this.totalTime(this._time+(n-1)*r,s):this._repeat?Fe(this._tTime,r)+1:1},t.timeScale=function(n,s){if(!arguments.length)return this._rts===-F?0:this._rts;if(this._rts===n)return this;var r=this.parent&&this._ts?Wi(this.parent._time,this):this._tTime;return this._rts=+n||0,this._ts=this._ps||n===-F?0:this._rts,this.totalTime(wi(-Math.abs(this._delay),this.totalDuration(),r),s!==!1),Qi(this),mp(this)},t.paused=function(n){return arguments.length?(this._ps!==n&&(this._ps=n,n?(this._pTime=this._tTime||Math.max(-this._delay,this.rawTime()),this._ts=this._act=0):(Be(),this._ts=this._rts,this.totalTime(this.parent&&!this.parent.smoothChildTiming?this.rawTime():this._tTime||this._pTime,this.progress()===1&&Math.abs(this._zTime)!==F&&(this._tTime-=F)))),this):this._ps},t.startTime=function(n){if(arguments.length){this._start=n;var s=this.parent||this._dp;return s&&(s._sort||!this.parent)&&Mt(s,this,n-this._delay),this}return this._start},t.endTime=function(n){return this._start+(lt(n)?this.totalDuration():this.duration())/Math.abs(this._ts||1)},t.rawTime=function(n){var s=this.parent||this._dp;return s?n&&(!this._ts||this._repeat&&this._time&&this.totalProgress()<1)?this._tTime%(this._dur+this._rDelay):this._ts?Wi(s.rawTime(n),this):this._tTime:this._tTime},t.revert=function(n){n===void 0&&(n=hp);var s=et;return et=n,Ks(this)&&(this.timeline&&this.timeline.revert(n),this.totalTime(-.01,n.suppressEvents)),this.data!=="nested"&&n.kill!==!1&&this.kill(),et=s,this},t.globalTime=function(n){for(var s=this,r=arguments.length?n:s.rawTime();s;)r=s._start+r/(Math.abs(s._ts)||1),s=s._dp;return!this.parent&&this._sat?this._sat.globalTime(n):r},t.repeat=function(n){return arguments.length?(this._repeat=n===1/0?-2:n,wo(this)):this._repeat===-2?1/0:this._repeat},t.repeatDelay=function(n){if(arguments.length){var s=this._time;return this._rDelay=n,wo(this),s?this.time(s):this}return this._rDelay},t.yoyo=function(n){return arguments.length?(this._yoyo=n,this):this._yoyo},t.seek=function(n,s){return this.totalTime(Tt(this,n),lt(s))},t.restart=function(n,s){return this.play().totalTime(n?-this._delay:0,lt(s)),this._dur||(this._zTime=-F),this},t.play=function(n,s){return n!=null&&this.seek(n,s),this.reversed(!1).paused(!1)},t.reverse=function(n,s){return n!=null&&this.seek(n||this.totalDuration(),s),this.reversed(!0).paused(!1)},t.pause=function(n,s){return n!=null&&this.seek(n,s),this.paused(!0)},t.resume=function(){return this.paused(!1)},t.reversed=function(n){return arguments.length?(!!n!==this.reversed()&&this.timeScale(-this._rts||(n?-F:0)),this):this._rts<0},t.invalidate=function(){return this._initted=this._act=0,this._zTime=-F,this},t.isActive=function(){var n=this.parent||this._dp,s=this._start,r;return!!(!n||this._ts&&this._initted&&n.isActive()&&(r=n.rawTime(!0))>=s&&r<this.endTime(!0)-F)},t.eventCallback=function(n,s,r){var o=this.vars;return arguments.length>1?(s?(o[n]=s,r&&(o[n+"Params"]=r),n==="onUpdate"&&(this._onUpdate=s)):delete o[n],this):o[n]},t.then=function(n){var s=this;return new Promise(function(r){var o=W(n)?n:Ml,a=function(){var c=s.then;s.then=null,W(o)&&(o=o(s))&&(o.then||o===s)&&(s.then=c),r(o),s.then=c};s._initted&&s.totalProgress()===1&&s._ts>=0||!s._tTime&&s._ts<0?a():s._prom=a})},t.kill=function(){He(this)},e}();_t(mi.prototype,{_time:0,_start:0,_end:0,_tTime:0,_tDur:0,_dirty:0,_repeat:0,_yoyo:!1,parent:null,_initted:!1,_rDelay:0,_ts:1,_dp:0,ratio:0,_zTime:-F,_prom:0,_ps:!1,_rts:1});var rt=function(e){ml(t,e);function t(n,s){var r;return n===void 0&&(n={}),r=e.call(this,n)||this,r.labels={},r.smoothChildTiming=!!n.smoothChildTiming,r.autoRemoveChildren=!!n.autoRemoveChildren,r._sort=lt(n.sortChildren),B&&Mt(n.parent||B,Ft(r),s),n.reversed&&r.reverse(),n.paused&&r.paused(!0),n.scrollTrigger&&El(Ft(r),n.scrollTrigger),r}var i=t.prototype;return i.to=function(s,r,o){return ii(0,arguments,this),this},i.from=function(s,r,o){return ii(1,arguments,this),this},i.fromTo=function(s,r,o,a){return ii(2,arguments,this),this},i.set=function(s,r,o){return r.duration=0,r.parent=this,ei(r).repeatDelay||(r.repeat=0),r.immediateRender=!!r.immediateRender,new q(s,r,Tt(this,o),1),this},i.call=function(s,r,o){return Mt(this,q.delayedCall(0,s,r),o)},i.staggerTo=function(s,r,o,a,l,c,u){return o.duration=r,o.stagger=o.stagger||a,o.onComplete=c,o.onCompleteParams=u,o.parent=this,new q(s,o,Tt(this,l)),this},i.staggerFrom=function(s,r,o,a,l,c,u){return o.runBackwards=1,ei(o).immediateRender=lt(o.immediateRender),this.staggerTo(s,r,o,a,l,c,u)},i.staggerFromTo=function(s,r,o,a,l,c,u,h){return a.startAt=o,ei(a).immediateRender=lt(a.immediateRender),this.staggerTo(s,r,a,l,c,u,h)},i.render=function(s,r,o){var a=this._time,l=this._dirty?this.totalDuration():this._tDur,c=this._dur,u=s<=0?0:Z(s),h=this._zTime<0!=s<0&&(this._initted||!c),f,d,m,p,g,y,x,v,w,_,T,b;if(this!==B&&u>l&&s>=0&&(u=l),u!==this._tTime||o||h){if(a!==this._time&&c&&(u+=this._time-a,s+=this._time-a),f=u,w=this._start,v=this._ts,y=!v,h&&(c||(a=this._zTime),(s||!r)&&(this._zTime=s)),this._repeat){if(T=this._yoyo,g=c+this._rDelay,this._repeat<-1&&s<0)return this.totalTime(g*100+s,r,o);if(f=Z(u%g),u===l?(p=this._repeat,f=c):(_=Z(u/g),p=~~_,p&&p===_&&(f=c,p--),f>c&&(f=c)),_=Fe(this._tTime,g),!a&&this._tTime&&_!==p&&this._tTime-_*g-this._dur<=0&&(_=p),T&&p&1&&(f=c-f,b=1),p!==_&&!this._lock){var S=T&&_&1,P=S===(T&&p&1);if(p<_&&(S=!S),a=S?0:u%c?c:u,this._lock=1,this.render(a||(b?0:Z(p*g)),r,!c)._lock=0,this._tTime=u,!r&&this.parent&&mt(this,"onRepeat"),this.vars.repeatRefresh&&!b&&(this.invalidate()._lock=1),a&&a!==this._time||y!==!this._ts||this.vars.onRepeat&&!this.parent&&!this._act)return this;if(c=this._dur,l=this._tDur,P&&(this._lock=2,a=S?c:-1e-4,this.render(a,!0),this.vars.repeatRefresh&&!b&&this.invalidate()),this._lock=0,!this._ts&&!y)return this;$l(this,b)}}if(this._hasPause&&!this._forcing&&this._lock<2&&(x=vp(this,Z(a),Z(f)),x&&(u-=f-(f=x._start))),this._tTime=u,this._time=f,this._act=!v,this._initted||(this._onUpdate=this.vars.onUpdate,this._initted=1,this._zTime=s,a=0),!a&&u&&!r&&!_&&(mt(this,"onStart"),this._tTime!==u))return this;if(f>=a&&s>=0)for(d=this._first;d;){if(m=d._next,(d._act||f>=d._start)&&d._ts&&x!==d){if(d.parent!==this)return this.render(s,r,o);if(d.render(d._ts>0?(f-d._start)*d._ts:(d._dirty?d.totalDuration():d._tDur)+(f-d._start)*d._ts,r,o),f!==this._time||!this._ts&&!y){x=0,m&&(u+=this._zTime=-F);break}}d=m}else{d=this._last;for(var D=s<0?s:f;d;){if(m=d._prev,(d._act||D<=d._end)&&d._ts&&x!==d){if(d.parent!==this)return this.render(s,r,o);if(d.render(d._ts>0?(D-d._start)*d._ts:(d._dirty?d.totalDuration():d._tDur)+(D-d._start)*d._ts,r,o||et&&Ks(d)),f!==this._time||!this._ts&&!y){x=0,m&&(u+=this._zTime=D?-F:F);break}}d=m}}if(x&&!r&&(this.pause(),x.render(f>=a?0:-F)._zTime=f>=a?1:-1,this._ts))return this._start=w,Qi(this),this.render(s,r,o);this._onUpdate&&!r&&mt(this,"onUpdate",!0),(u===l&&this._tTime>=this.totalDuration()||!u&&a)&&(w===this._start||Math.abs(v)!==Math.abs(this._ts))&&(this._lock||((s||!c)&&(u===l&&this._ts>0||!u&&this._ts<0)&&Jt(this,1),!r&&!(s<0&&!a)&&(u||a||!l)&&(mt(this,u===l&&s>=0?"onComplete":"onReverseComplete",!0),this._prom&&!(u<l&&this.timeScale()>0)&&this._prom())))}return this},i.add=function(s,r){var o=this;if(jt(r)||(r=Tt(this,r,s)),!(s instanceof mi)){if(st(s))return s.forEach(function(a){return o.add(a,r)}),this;if(J(s))return this.addLabel(s,r);if(W(s))s=q.delayedCall(0,s);else return this}return this!==s?Mt(this,s,r):this},i.getChildren=function(s,r,o,a){s===void 0&&(s=!0),r===void 0&&(r=!0),o===void 0&&(o=!0),a===void 0&&(a=-wt);for(var l=[],c=this._first;c;)c._start>=a&&(c instanceof q?r&&l.push(c):(o&&l.push(c),s&&l.push.apply(l,c.getChildren(!0,r,o)))),c=c._next;return l},i.getById=function(s){for(var r=this.getChildren(1,1,1),o=r.length;o--;)if(r[o].vars.id===s)return r[o]},i.remove=function(s){return J(s)?this.removeLabel(s):W(s)?this.killTweensOf(s):(s.parent===this&&Zi(this,s),s===this._recent&&(this._recent=this._last),ge(this))},i.totalTime=function(s,r){return arguments.length?(this._forcing=1,!this._dp&&this._ts&&(this._start=Z(pt.time-(this._ts>0?s/this._ts:(this.totalDuration()-s)/-this._ts))),e.prototype.totalTime.call(this,s,r),this._forcing=0,this):this._tTime},i.addLabel=function(s,r){return this.labels[s]=Tt(this,r),this},i.removeLabel=function(s){return delete this.labels[s],this},i.addPause=function(s,r,o){var a=q.delayedCall(0,r||fi,o);return a.data="isPause",this._hasPause=1,Mt(this,a,Tt(this,s))},i.removePause=function(s){var r=this._first;for(s=Tt(this,s);r;)r._start===s&&r.data==="isPause"&&Jt(r),r=r._next},i.killTweensOf=function(s,r,o){for(var a=this.getTweensOf(s,o),l=a.length;l--;)$t!==a[l]&&a[l].kill(s,r);return this},i.getTweensOf=function(s,r){for(var o=[],a=St(s),l=this._first,c=jt(r),u;l;)l instanceof q?fp(l._targets,a)&&(c?(!$t||l._initted&&l._ts)&&l.globalTime(0)<=r&&l.globalTime(l.totalDuration())>r:!r||l.isActive())&&o.push(l):(u=l.getTweensOf(a,r)).length&&o.push.apply(o,u),l=l._next;return o},i.tweenTo=function(s,r){r=r||{};var o=this,a=Tt(o,s),l=r,c=l.startAt,u=l.onStart,h=l.onStartParams,f=l.immediateRender,d,m=q.to(o,_t({ease:r.ease||"none",lazy:!1,immediateRender:!1,time:a,overwrite:"auto",duration:r.duration||Math.abs((a-(c&&"time"in c?c.time:o._time))/o.timeScale())||F,onStart:function(){if(o.pause(),!d){var g=r.duration||Math.abs((a-(c&&"time"in c?c.time:o._time))/o.timeScale());m._dur!==g&&Ie(m,g,0,1).render(m._time,!0,!0),d=1}u&&u.apply(m,h||[])}},r));return f?m.render(0):m},i.tweenFromTo=function(s,r,o){return this.tweenTo(r,_t({startAt:{time:Tt(this,s)}},o))},i.recent=function(){return this._recent},i.nextLabel=function(s){return s===void 0&&(s=this._time),So(this,Tt(this,s))},i.previousLabel=function(s){return s===void 0&&(s=this._time),So(this,Tt(this,s),1)},i.currentLabel=function(s){return arguments.length?this.seek(s,!0):this.previousLabel(this._time+F)},i.shiftChildren=function(s,r,o){o===void 0&&(o=0);for(var a=this._first,l=this.labels,c;a;)a._start>=o&&(a._start+=s,a._end+=s),a=a._next;if(r)for(c in l)l[c]>=o&&(l[c]+=s);return ge(this)},i.invalidate=function(s){var r=this._first;for(this._lock=0;r;)r.invalidate(s),r=r._next;return e.prototype.invalidate.call(this,s)},i.clear=function(s){s===void 0&&(s=!0);for(var r=this._first,o;r;)o=r._next,this.remove(r),r=o;return this._dp&&(this._time=this._tTime=this._pTime=0),s&&(this.labels={}),ge(this)},i.totalDuration=function(s){var r=0,o=this,a=o._last,l=wt,c,u,h;if(arguments.length)return o.timeScale((o._repeat<0?o.duration():o.totalDuration())/(o.reversed()?-s:s));if(o._dirty){for(h=o.parent;a;)c=a._prev,a._dirty&&a.totalDuration(),u=a._start,u>l&&o._sort&&a._ts&&!o._lock?(o._lock=1,Mt(o,a,u-a._delay,1)._lock=0):l=u,u<0&&a._ts&&(r-=u,(!h&&!o._dp||h&&h.smoothChildTiming)&&(o._start+=u/o._ts,o._time-=u,o._tTime-=u),o.shiftChildren(-u,!1,-1/0),l=0),a._end>r&&a._ts&&(r=a._end),a=c;Ie(o,o===B&&o._time>r?o._time:r,1,1),o._dirty=0}return o._tDur},t.updateRoot=function(s){if(B._ts&&(Al(B,Wi(s,B)),bl=pt.frame),pt.frame>=vo){vo+=gt.autoSleep||120;var r=B._first;if((!r||!r._ts)&&gt.autoSleep&&pt._listeners.length<2){for(;r&&!r._ts;)r=r._next;r||pt.sleep()}}},t}(mi);_t(rt.prototype,{_lock:0,_hasPause:0,_forcing:0});var Lp=function(t,i,n,s,r,o,a){var l=new ct(this._pt,t,i,0,1,tu,null,r),c=0,u=0,h,f,d,m,p,g,y,x;for(l.b=n,l.e=s,n+="",s+="",(y=~s.indexOf("random("))&&(s=di(s)),o&&(x=[n,s],o(x,t,i),n=x[0],s=x[1]),f=n.match(mn)||[];h=mn.exec(s);)m=h[0],p=s.substring(c,h.index),d?d=(d+1)%5:p.substr(-5)==="rgba("&&(d=1),m!==f[u++]&&(g=parseFloat(f[u-1])||0,l._pt={_next:l._pt,p:p||u===1?p:",",s:g,c:m.charAt(1)==="="?De(g,m)-g:parseFloat(m)-g,m:d&&d<4?Math.round:0},c=mn.lastIndex);return l.c=c<s.length?s.substring(c,s.length):"",l.fp=a,(vl.test(s)||y)&&(l.e=0),this._pt=l,l},Hs=function(t,i,n,s,r,o,a,l,c,u){W(s)&&(s=s(r||0,t,o));var h=t[i],f=n!=="get"?n:W(h)?c?t[i.indexOf("set")||!W(t["get"+i.substr(3)])?i:"get"+i.substr(3)](c):t[i]():h,d=W(h)?c?zp:Ql:qs,m;if(J(s)&&(~s.indexOf("random(")&&(s=di(s)),s.charAt(1)==="="&&(m=De(f,s)+(it(f)||0),(m||m===0)&&(s=m))),!u||f!==s||Qn)return!isNaN(f*s)&&s!==""?(m=new ct(this._pt,t,i,+f||0,s-(f||0),typeof h=="boolean"?Up:Jl,0,d),c&&(m.fp=c),a&&m.modifier(a,this,t),this._pt=m):(!h&&!(i in t)&&Ws(i,s),Lp.call(this,t,i,f,s,d,l||gt.stringFilter,c))},Fp=function(t,i,n,s,r){if(W(t)&&(t=ni(t,r,i,n,s)),!kt(t)||t.style&&t.nodeType||st(t)||yl(t))return J(t)?ni(t,r,i,n,s):t;var o={},a;for(a in t)o[a]=ni(t[a],r,i,n,s);return o},Gl=function(t,i,n,s,r,o){var a,l,c,u;if(dt[t]&&(a=new dt[t]).init(r,a.rawVars?i[t]:Fp(i[t],s,r,o,n),n,s,o)!==!1&&(n._pt=l=new ct(n._pt,r,t,0,1,a.render,a,0,a.priority),n!==Me))for(c=n._ptLookup[n._targets.indexOf(r)],u=a._props.length;u--;)c[a._props[u]]=l;return a},$t,Qn,Gs=function e(t,i,n){var s=t.vars,r=s.ease,o=s.startAt,a=s.immediateRender,l=s.lazy,c=s.onUpdate,u=s.runBackwards,h=s.yoyoEase,f=s.keyframes,d=s.autoRevert,m=t._dur,p=t._startAt,g=t._targets,y=t.parent,x=y&&y.data==="nested"?y.vars.targets:g,v=t._overwrite==="auto"&&!Ns,w=t.timeline,_,T,b,S,P,D,V,k,O,H,Q,Y,E;if(w&&(!f||!r)&&(r="none"),t._ease=ye(r,Oe.ease),t._yEase=h?Xl(ye(h===!0?r:h,Oe.ease)):0,h&&t._yoyo&&!t._repeat&&(h=t._yEase,t._yEase=t._ease,t._ease=h),t._from=!w&&!!s.runBackwards,!w||f&&!s.stagger){if(k=g[0]?me(g[0]).harness:0,Y=k&&s[k.prop],_=Ui(s,Ys),p&&(p._zTime<0&&p.progress(1),i<0&&u&&a&&!d?p.render(-1,!0):p.revert(u&&m?Vi:cp),p._lazy=0),o){if(Jt(t._startAt=q.set(g,_t({data:"isStart",overwrite:!1,parent:y,immediateRender:!0,lazy:!p&&lt(l),startAt:null,delay:0,onUpdate:c&&function(){return mt(t,"onUpdate")},stagger:0},o))),t._startAt._dp=0,t._startAt._sat=t,i<0&&(et||!a&&!d)&&t._startAt.revert(Vi),a&&m&&i<=0&&n<=0){i&&(t._zTime=i);return}}else if(u&&m&&!p){if(i&&(a=!1),b=_t({overwrite:!1,data:"isFromStart",lazy:a&&!p&&lt(l),immediateRender:a,stagger:0,parent:y},_),Y&&(b[k.prop]=Y),Jt(t._startAt=q.set(g,b)),t._startAt._dp=0,t._startAt._sat=t,i<0&&(et?t._startAt.revert(Vi):t._startAt.render(-1,!0)),t._zTime=i,!a)e(t._startAt,F,F);else if(!i)return}for(t._pt=t._ptCache=0,l=m&&lt(l)||l&&!m,T=0;T<g.length;T++){if(P=g[T],V=P._gsap||$s(g)[T]._gsap,t._ptLookup[T]=H={},$n[V.id]&&Gt.length&&ji(),Q=x===g?T:x.indexOf(P),k&&(O=new k).init(P,Y||_,t,Q,x)!==!1&&(t._pt=S=new ct(t._pt,P,O.name,0,1,O.render,O,0,O.priority),O._props.forEach(function(X){H[X]=S}),O.priority&&(D=1)),!k||Y)for(b in _)dt[b]&&(O=Gl(b,_,t,Q,P,x))?O.priority&&(D=1):H[b]=S=Hs.call(t,P,b,"get",_[b],Q,x,0,s.stringFilter);t._op&&t._op[T]&&t.kill(P,t._op[T]),v&&t._pt&&($t=t,B.killTweensOf(P,H,t.globalTime(i)),E=!t.parent,$t=0),t._pt&&l&&($n[V.id]=1)}D&&eu(t),t._onInit&&t._onInit(t)}t._onUpdate=c,t._initted=(!t._op||t._pt)&&!E,f&&i<=0&&w.render(wt,!0,!0)},Ip=function(t,i,n,s,r,o,a,l){var c=(t._pt&&t._ptCache||(t._ptCache={}))[i],u,h,f,d;if(!c)for(c=t._ptCache[i]=[],f=t._ptLookup,d=t._targets.length;d--;){if(u=f[d][i],u&&u.d&&u.d._pt)for(u=u.d._pt;u&&u.p!==i&&u.fp!==i;)u=u._next;if(!u)return Qn=1,t.vars[i]="+=0",Gs(t,a),Qn=0,l?hi(i+" not eligible for reset"):1;c.push(u)}for(d=c.length;d--;)h=c[d],u=h._pt||h,u.s=(s||s===0)&&!r?s:u.s+(s||0)+o*u.c,u.c=n-u.s,h.e&&(h.e=K(n)+it(h.e)),h.b&&(h.b=u.s+it(h.b))},Bp=function(t,i){var n=t[0]?me(t[0]).harness:0,s=n&&n.aliases,r,o,a,l;if(!s)return i;r=Le({},i);for(o in s)if(o in r)for(l=s[o].split(","),a=l.length;a--;)r[l[a]]=r[o];return r},Np=function(t,i,n,s){var r=i.ease||s||"power1.inOut",o,a;if(st(i))a=n[t]||(n[t]=[]),i.forEach(function(l,c){return a.push({t:c/(i.length-1)*100,v:l,e:r})});else for(o in i)a=n[o]||(n[o]=[]),o==="ease"||a.push({t:parseFloat(t),v:i[o],e:r})},ni=function(t,i,n,s,r){return W(t)?t.call(i,n,s,r):J(t)&&~t.indexOf("random(")?di(t):t},ql=Xs+"repeat,repeatDelay,yoyo,repeatRefresh,yoyoEase,autoRevert",Zl={};ut(ql+",id,stagger,delay,duration,paused,scrollTrigger",function(e){return Zl[e]=1});var q=function(e){ml(t,e);function t(n,s,r,o){var a;typeof s=="number"&&(r.duration=s,s=r,r=null),a=e.call(this,o?s:ei(s))||this;var l=a.vars,c=l.duration,u=l.delay,h=l.immediateRender,f=l.stagger,d=l.overwrite,m=l.keyframes,p=l.defaults,g=l.scrollTrigger,y=l.yoyoEase,x=s.parent||B,v=(st(n)||yl(n)?jt(n[0]):"length"in s)?[n]:St(n),w,_,T,b,S,P,D,V;if(a._targets=v.length?$s(v):hi("GSAP target "+n+" not found. https://gsap.com",!gt.nullTargetWarn)||[],a._ptLookup=[],a._overwrite=d,m||f||Ci(c)||Ci(u)){if(s=a.vars,w=a.timeline=new rt({data:"nested",defaults:p||{},targets:x&&x.data==="nested"?x.vars.targets:v}),w.kill(),w.parent=w._dp=Ft(a),w._start=0,f||Ci(c)||Ci(u)){if(b=v.length,D=f&&Ll(f),kt(f))for(S in f)~ql.indexOf(S)&&(V||(V={}),V[S]=f[S]);for(_=0;_<b;_++)T=Ui(s,Zl),T.stagger=0,y&&(T.yoyoEase=y),V&&Le(T,V),P=v[_],T.duration=+ni(c,Ft(a),_,P,v),T.delay=(+ni(u,Ft(a),_,P,v)||0)-a._delay,!f&&b===1&&T.delay&&(a._delay=u=T.delay,a._start+=u,T.delay=0),w.to(P,T,D?D(_,P,v):0),w._ease=R.none;w.duration()?c=u=0:a.timeline=0}else if(m){ei(_t(w.vars.defaults,{ease:"none"})),w._ease=ye(m.ease||s.ease||"none");var k=0,O,H,Q;if(st(m))m.forEach(function(Y){return w.to(v,Y,">")}),w.duration();else{T={};for(S in m)S==="ease"||S==="easeEach"||Np(S,m[S],T,m.easeEach);for(S in T)for(O=T[S].sort(function(Y,E){return Y.t-E.t}),k=0,_=0;_<O.length;_++)H=O[_],Q={ease:H.e,duration:(H.t-(_?O[_-1].t:0))/100*c},Q[S]=H.v,w.to(v,Q,k),k+=Q.duration;w.duration()<c&&w.to({},{duration:c-w.duration()})}}c||a.duration(c=w.duration())}else a.timeline=0;return d===!0&&!Ns&&($t=Ft(a),B.killTweensOf(v),$t=0),Mt(x,Ft(a),r),s.reversed&&a.reverse(),s.paused&&a.paused(!0),(h||!c&&!m&&a._start===Z(x._time)&&lt(h)&&gp(Ft(a))&&x.data!=="nested")&&(a._tTime=-F,a.render(Math.max(0,-u)||0)),g&&El(Ft(a),g),a}var i=t.prototype;return i.render=function(s,r,o){var a=this._time,l=this._tDur,c=this._dur,u=s<0,h=s>l-F&&!u?l:s<F?0:s,f,d,m,p,g,y,x,v,w;if(!c)_p(this,s,r,o);else if(h!==this._tTime||!s||o||!this._initted&&this._tTime||this._startAt&&this._zTime<0!==u||this._lazy){if(f=h,v=this.timeline,this._repeat){if(p=c+this._rDelay,this._repeat<-1&&u)return this.totalTime(p*100+s,r,o);if(f=Z(h%p),h===l?(m=this._repeat,f=c):(g=Z(h/p),m=~~g,m&&m===g?(f=c,m--):f>c&&(f=c)),y=this._yoyo&&m&1,y&&(w=this._yEase,f=c-f),g=Fe(this._tTime,p),f===a&&!o&&this._initted&&m===g)return this._tTime=h,this;m!==g&&(v&&this._yEase&&$l(v,y),this.vars.repeatRefresh&&!y&&!this._lock&&f!==p&&this._initted&&(this._lock=o=1,this.render(Z(p*m),!0).invalidate()._lock=0))}if(!this._initted){if(Vl(this,u?s:f,o,r,h))return this._tTime=0,this;if(a!==this._time&&!(o&&this.vars.repeatRefresh&&m!==g))return this;if(c!==this._dur)return this.render(s,r,o)}if(this._tTime=h,this._time=f,!this._act&&this._ts&&(this._act=1,this._lazy=0),this.ratio=x=(w||this._ease)(f/c),this._from&&(this.ratio=x=1-x),!a&&h&&!r&&!g&&(mt(this,"onStart"),this._tTime!==h))return this;for(d=this._pt;d;)d.r(x,d.d),d=d._next;v&&v.render(s<0?s:v._dur*v._ease(f/this._dur),r,o)||this._startAt&&(this._zTime=s),this._onUpdate&&!r&&(u&&Kn(this,s,r,o),mt(this,"onUpdate")),this._repeat&&m!==g&&this.vars.onRepeat&&!r&&this.parent&&mt(this,"onRepeat"),(h===this._tDur||!h)&&this._tTime===h&&(u&&!this._onUpdate&&Kn(this,s,!0,!0),(s||!c)&&(h===this._tDur&&this._ts>0||!h&&this._ts<0)&&Jt(this,1),!r&&!(u&&!a)&&(h||a||y)&&(mt(this,h===l?"onComplete":"onReverseComplete",!0),this._prom&&!(h<l&&this.timeScale()>0)&&this._prom()))}return this},i.targets=function(){return this._targets},i.invalidate=function(s){return(!s||!this.vars.runBackwards)&&(this._startAt=0),this._pt=this._op=this._onUpdate=this._lazy=this.ratio=0,this._ptLookup=[],this.timeline&&this.timeline.invalidate(s),e.prototype.invalidate.call(this,s)},i.resetTo=function(s,r,o,a,l){pi||pt.wake(),this._ts||this.play();var c=Math.min(this._dur,(this._dp._time-this._start)*this._ts),u;return this._initted||Gs(this,c),u=this._ease(c/this._dur),Ip(this,s,r,o,a,u,c,l)?this.resetTo(s,r,o,a,1):(Ji(this,0),this.parent||Dl(this._dp,this,"_first","_last",this._dp._sort?"_start":0),this.render(0))},i.kill=function(s,r){if(r===void 0&&(r="all"),!s&&(!r||r==="all"))return this._lazy=this._pt=0,this.parent?He(this):this.scrollTrigger&&this.scrollTrigger.kill(!!et),this;if(this.timeline){var o=this.timeline.totalDuration();return this.timeline.killTweensOf(s,r,$t&&$t.vars.overwrite!==!0)._first||He(this),this.parent&&o!==this.timeline.totalDuration()&&Ie(this,this._dur*this.timeline._tDur/o,0,1),this}var a=this._targets,l=s?St(s):a,c=this._ptLookup,u=this._pt,h,f,d,m,p,g,y;if((!r||r==="all")&&pp(a,l))return r==="all"&&(this._pt=0),He(this);for(h=this._op=this._op||[],r!=="all"&&(J(r)&&(p={},ut(r,function(x){return p[x]=1}),r=p),r=Bp(a,r)),y=a.length;y--;)if(~l.indexOf(a[y])){f=c[y],r==="all"?(h[y]=r,m=f,d={}):(d=h[y]=h[y]||{},m=r);for(p in m)g=f&&f[p],g&&((!("kill"in g.d)||g.d.kill(p)===!0)&&Zi(this,g,"_pt"),delete f[p]),d!=="all"&&(d[p]=1)}return this._initted&&!this._pt&&u&&He(this),this},t.to=function(s,r){return new t(s,r,arguments[2])},t.from=function(s,r){return ii(1,arguments)},t.delayedCall=function(s,r,o,a){return new t(r,0,{immediateRender:!1,lazy:!1,overwrite:!1,delay:s,onComplete:r,onReverseComplete:r,onCompleteParams:o,onReverseCompleteParams:o,callbackScope:a})},t.fromTo=function(s,r,o){return ii(2,arguments)},t.set=function(s,r){return r.duration=0,r.repeatDelay||(r.repeat=0),new t(s,r)},t.killTweensOf=function(s,r,o){return B.killTweensOf(s,r,o)},t}(mi);_t(q.prototype,{_targets:[],_lazy:0,_startAt:0,_op:0,_onInit:0});ut("staggerTo,staggerFrom,staggerFromTo",function(e){q[e]=function(){var t=new rt,i=Gn.call(arguments,0);return i.splice(e==="staggerFromTo"?5:4,0,0),t[e].apply(t,i)}});var qs=function(t,i,n){return t[i]=n},Ql=function(t,i,n){return t[i](n)},zp=function(t,i,n,s){return t[i](s.fp,n)},jp=function(t,i,n){return t.setAttribute(i,n)},Zs=function(t,i){return W(t[i])?Ql:zs(t[i])&&t.setAttribute?jp:qs},Jl=function(t,i){return i.set(i.t,i.p,Math.round((i.s+i.c*t)*1e6)/1e6,i)},Up=function(t,i){return i.set(i.t,i.p,!!(i.s+i.c*t),i)},tu=function(t,i){var n=i._pt,s="";if(!t&&i.b)s=i.b;else if(t===1&&i.e)s=i.e;else{for(;n;)s=n.p+(n.m?n.m(n.s+n.c*t):Math.round((n.s+n.c*t)*1e4)/1e4)+s,n=n._next;s+=i.c}i.set(i.t,i.p,s,i)},Qs=function(t,i){for(var n=i._pt;n;)n.r(t,n.d),n=n._next},Wp=function(t,i,n,s){for(var r=this._pt,o;r;)o=r._next,r.p===s&&r.modifier(t,i,n),r=o},Yp=function(t){for(var i=this._pt,n,s;i;)s=i._next,i.p===t&&!i.op||i.op===t?Zi(this,i,"_pt"):i.dep||(n=1),i=s;return!n},Xp=function(t,i,n,s){s.mSet(t,i,s.m.call(s.tween,n,s.mt),s)},eu=function(t){for(var i=t._pt,n,s,r,o;i;){for(n=i._next,s=r;s&&s.pr>i.pr;)s=s._next;(i._prev=s?s._prev:o)?i._prev._next=i:r=i,(i._next=s)?s._prev=i:o=i,i=n}t._pt=r},ct=function(){function e(i,n,s,r,o,a,l,c,u){this.t=n,this.s=r,this.c=o,this.p=s,this.r=a||Jl,this.d=l||this,this.set=c||qs,this.pr=u||0,this._next=i,i&&(i._prev=this)}var t=e.prototype;return t.modifier=function(n,s,r){this.mSet=this.mSet||this.set,this.set=Xp,this.m=n,this.mt=r,this.tween=s},e}();ut(Xs+"parent,duration,ease,delay,overwrite,runBackwards,startAt,yoyo,immediateRender,repeat,repeatDelay,data,paused,reversed,lazy,callbackScope,stringFilter,id,yoyoEase,stagger,inherit,repeatRefresh,keyframes,autoRevert,scrollTrigger",function(e){return Ys[e]=1});yt.TweenMax=yt.TweenLite=q;yt.TimelineLite=yt.TimelineMax=rt;B=new rt({sortChildren:!1,defaults:Oe,autoRemoveChildren:!0,id:"root",smoothChildTiming:!0});gt.stringFilter=Yl;var _e=[],Oi={},$p=[],Po=0,Kp=0,xn=function(t){return(Oi[t]||$p).map(function(i){return i()})},Jn=function(){var t=Date.now(),i=[];t-Po>2&&(xn("matchMediaInit"),_e.forEach(function(n){var s=n.queries,r=n.conditions,o,a,l,c;for(a in s)o=Ct.matchMedia(s[a]).matches,o&&(l=1),o!==r[a]&&(r[a]=o,c=1);c&&(n.revert(),l&&i.push(n))}),xn("matchMediaRevert"),i.forEach(function(n){return n.onMatch(n,function(s){return n.add(null,s)})}),Po=t,xn("matchMedia"))},iu=function(){function e(i,n){this.selector=n&&qn(n),this.data=[],this._r=[],this.isReverted=!1,this.id=Kp++,i&&this.add(i)}var t=e.prototype;return t.add=function(n,s,r){W(n)&&(r=s,s=n,n=W);var o=this,a=function(){var c=I,u=o.selector,h;return c&&c!==o&&c.data.push(o),r&&(o.selector=qn(r)),I=o,h=s.apply(o,arguments),W(h)&&o._r.push(h),I=c,o.selector=u,o.isReverted=!1,h};return o.last=a,n===W?a(o,function(l){return o.add(null,l)}):n?o[n]=a:a},t.ignore=function(n){var s=I;I=null,n(this),I=s},t.getTweens=function(){var n=[];return this.data.forEach(function(s){return s instanceof e?n.push.apply(n,s.getTweens()):s instanceof q&&!(s.parent&&s.parent.data==="nested")&&n.push(s)}),n},t.clear=function(){this._r.length=this.data.length=0},t.kill=function(n,s){var r=this;if(n?function(){for(var a=r.getTweens(),l=r.data.length,c;l--;)c=r.data[l],c.data==="isFlip"&&(c.revert(),c.getChildren(!0,!0,!1).forEach(function(u){return a.splice(a.indexOf(u),1)}));for(a.map(function(u){return{g:u._dur||u._delay||u._sat&&!u._sat.vars.immediateRender?u.globalTime(0):-1/0,t:u}}).sort(function(u,h){return h.g-u.g||-1/0}).forEach(function(u){return u.t.revert(n)}),l=r.data.length;l--;)c=r.data[l],c instanceof rt?c.data!=="nested"&&(c.scrollTrigger&&c.scrollTrigger.revert(),c.kill()):!(c instanceof q)&&c.revert&&c.revert(n);r._r.forEach(function(u){return u(n,r)}),r.isReverted=!0}():this.data.forEach(function(a){return a.kill&&a.kill()}),this.clear(),s)for(var o=_e.length;o--;)_e[o].id===this.id&&_e.splice(o,1)},t.revert=function(n){this.kill(n||{})},e}(),Hp=function(){function e(i){this.contexts=[],this.scope=i,I&&I.data.push(this)}var t=e.prototype;return t.add=function(n,s,r){kt(n)||(n={matches:n});var o=new iu(0,r||this.scope),a=o.conditions={},l,c,u;I&&!o.selector&&(o.selector=I.selector),this.contexts.push(o),s=o.add("onMatch",s),o.queries=n;for(c in n)c==="all"?u=1:(l=Ct.matchMedia(n[c]),l&&(_e.indexOf(o)<0&&_e.push(o),(a[c]=l.matches)&&(u=1),l.addListener?l.addListener(Jn):l.addEventListener("change",Jn)));return u&&s(o,function(h){return o.add(null,h)}),this},t.revert=function(n){this.kill(n||{})},t.kill=function(n){this.contexts.forEach(function(s){return s.kill(n,!0)})},e}(),Yi={registerPlugin:function(){for(var t=arguments.length,i=new Array(t),n=0;n<t;n++)i[n]=arguments[n];i.forEach(function(s){return jl(s)})},timeline:function(t){return new rt(t)},getTweensOf:function(t,i){return B.getTweensOf(t,i)},getProperty:function(t,i,n,s){J(t)&&(t=St(t)[0]);var r=me(t||{}).get,o=n?Ml:Cl;return n==="native"&&(n=""),t&&(i?o((dt[i]&&dt[i].get||r)(t,i,n,s)):function(a,l,c){return o((dt[a]&&dt[a].get||r)(t,a,l,c))})},quickSetter:function(t,i,n){if(t=St(t),t.length>1){var s=t.map(function(u){return ft.quickSetter(u,i,n)}),r=s.length;return function(u){for(var h=r;h--;)s[h](u)}}t=t[0]||{};var o=dt[i],a=me(t),l=a.harness&&(a.harness.aliases||{})[i]||i,c=o?function(u){var h=new o;Me._pt=0,h.init(t,n?u+n:u,Me,0,[t]),h.render(1,h),Me._pt&&Qs(1,Me)}:a.set(t,l);return o?c:function(u){return c(t,l,n?u+n:u,a,1)}},quickTo:function(t,i,n){var s,r=ft.to(t,_t((s={},s[i]="+=0.1",s.paused=!0,s.stagger=0,s),n||{})),o=function(l,c,u){return r.resetTo(i,l,c,u)};return o.tween=r,o},isTweening:function(t){return B.getTweensOf(t,!0).length>0},defaults:function(t){return t&&t.ease&&(t.ease=ye(t.ease,Oe.ease)),xo(Oe,t||{})},config:function(t){return xo(gt,t||{})},registerEffect:function(t){var i=t.name,n=t.effect,s=t.plugins,r=t.defaults,o=t.extendTimeline;(s||"").split(",").forEach(function(a){return a&&!dt[a]&&!yt[a]&&hi(i+" effect requires "+a+" plugin.")}),gn[i]=function(a,l,c){return n(St(a),_t(l||{},r),c)},o&&(rt.prototype[i]=function(a,l,c){return this.add(gn[i](a,kt(l)?l:(c=l)&&{},this),c)})},registerEase:function(t,i){R[t]=ye(i)},parseEase:function(t,i){return arguments.length?ye(t,i):R},getById:function(t){return B.getById(t)},exportRoot:function(t,i){t===void 0&&(t={});var n=new rt(t),s,r;for(n.smoothChildTiming=lt(t.smoothChildTiming),B.remove(n),n._dp=0,n._time=n._tTime=B._time,s=B._first;s;)r=s._next,(i||!(!s._dur&&s instanceof q&&s.vars.onComplete===s._targets[0]))&&Mt(n,s,s._start-s._delay),s=r;return Mt(B,n,0),n},context:function(t,i){return t?new iu(t,i):I},matchMedia:function(t){return new Hp(t)},matchMediaRefresh:function(){return _e.forEach(function(t){var i=t.conditions,n,s;for(s in i)i[s]&&(i[s]=!1,n=1);n&&t.revert()})||Jn()},addEventListener:function(t,i){var n=Oi[t]||(Oi[t]=[]);~n.indexOf(i)||n.push(i)},removeEventListener:function(t,i){var n=Oi[t],s=n&&n.indexOf(i);s>=0&&n.splice(s,1)},utils:{wrap:Ap,wrapYoyo:Cp,distribute:Ll,random:Il,snap:Fl,normalize:Pp,getUnit:it,clamp:Tp,splitColor:Ul,toArray:St,selector:qn,mapRange:Nl,pipe:Sp,unitize:bp,interpolate:Mp,shuffle:Ol},install:wl,effects:gn,ticker:pt,updateRoot:rt.updateRoot,plugins:dt,globalTimeline:B,core:{PropTween:ct,globals:Sl,Tween:q,Timeline:rt,Animation:mi,getCache:me,_removeLinkedListItem:Zi,reverting:function(){return et},context:function(t){return t&&I&&(I.data.push(t),t._ctx=I),I},suppressOverwrites:function(t){return Ns=t}}};ut("to,from,fromTo,delayedCall,set,killTweensOf",function(e){return Yi[e]=q[e]});pt.add(rt.updateRoot);Me=Yi.to({},{duration:0});var Gp=function(t,i){for(var n=t._pt;n&&n.p!==i&&n.op!==i&&n.fp!==i;)n=n._next;return n},qp=function(t,i){var n=t._targets,s,r,o;for(s in i)for(r=n.length;r--;)o=t._ptLookup[r][s],o&&(o=o.d)&&(o._pt&&(o=Gp(o,s)),o&&o.modifier&&o.modifier(i[s],t,n[r],s))},Tn=function(t,i){return{name:t,headless:1,rawVars:1,init:function(s,r,o){o._onInit=function(a){var l,c;if(J(r)&&(l={},ut(r,function(u){return l[u]=1}),r=l),i){l={};for(c in r)l[c]=i(r[c]);r=l}qp(a,r)}}}},ft=Yi.registerPlugin({name:"attr",init:function(t,i,n,s,r){var o,a,l;this.tween=n;for(o in i)l=t.getAttribute(o)||"",a=this.add(t,"setAttribute",(l||0)+"",i[o],s,r,0,0,o),a.op=o,a.b=l,this._props.push(o)},render:function(t,i){for(var n=i._pt;n;)et?n.set(n.t,n.p,n.b,n):n.r(t,n.d),n=n._next}},{name:"endArray",headless:1,init:function(t,i){for(var n=i.length;n--;)this.add(t,n,t[n]||0,i[n],0,0,0,0,0,1)}},Tn("roundProps",Zn),Tn("modifiers"),Tn("snap",Fl))||Yi;q.version=rt.version=ft.version="3.13.0";Tl=1;js()&&Be();R.Power0;R.Power1;R.Power2;R.Power3;R.Power4;R.Linear;R.Quad;R.Cubic;R.Quart;R.Quint;R.Strong;R.Elastic;R.Back;R.SteppedEase;R.Bounce;R.Sine;R.Expo;R.Circ;/*!
 * CSSPlugin 3.13.0
 * https://gsap.com
 *
 * Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var Ao,Kt,Re,Js,fe,Co,tr,Zp=function(){return typeof window<"u"},Ut={},ue=180/Math.PI,Ee=Math.PI/180,Te=Math.atan2,Mo=1e8,er=/([A-Z])/g,Qp=/(left|right|width|margin|padding|x)/i,Jp=/[\s,\(]\S/,Dt={autoAlpha:"opacity,visibility",scale:"scaleX,scaleY",alpha:"opacity"},ts=function(t,i){return i.set(i.t,i.p,Math.round((i.s+i.c*t)*1e4)/1e4+i.u,i)},tm=function(t,i){return i.set(i.t,i.p,t===1?i.e:Math.round((i.s+i.c*t)*1e4)/1e4+i.u,i)},em=function(t,i){return i.set(i.t,i.p,t?Math.round((i.s+i.c*t)*1e4)/1e4+i.u:i.b,i)},im=function(t,i){var n=i.s+i.c*t;i.set(i.t,i.p,~~(n+(n<0?-.5:.5))+i.u,i)},nu=function(t,i){return i.set(i.t,i.p,t?i.e:i.b,i)},su=function(t,i){return i.set(i.t,i.p,t!==1?i.b:i.e,i)},nm=function(t,i,n){return t.style[i]=n},sm=function(t,i,n){return t.style.setProperty(i,n)},rm=function(t,i,n){return t._gsap[i]=n},om=function(t,i,n){return t._gsap.scaleX=t._gsap.scaleY=n},am=function(t,i,n,s,r){var o=t._gsap;o.scaleX=o.scaleY=n,o.renderTransform(r,o)},lm=function(t,i,n,s,r){var o=t._gsap;o[i]=n,o.renderTransform(r,o)},z="transform",ht=z+"Origin",um=function e(t,i){var n=this,s=this.target,r=s.style,o=s._gsap;if(t in Ut&&r){if(this.tfm=this.tfm||{},t!=="transform")t=Dt[t]||t,~t.indexOf(",")?t.split(",").forEach(function(a){return n.tfm[a]=It(s,a)}):this.tfm[t]=o.x?o[t]:It(s,t),t===ht&&(this.tfm.zOrigin=o.zOrigin);else return Dt.transform.split(",").forEach(function(a){return e.call(n,a,i)});if(this.props.indexOf(z)>=0)return;o.svg&&(this.svgo=s.getAttribute("data-svg-origin"),this.props.push(ht,i,"")),t=z}(r||i)&&this.props.push(t,i,r[t])},ru=function(t){t.translate&&(t.removeProperty("translate"),t.removeProperty("scale"),t.removeProperty("rotate"))},cm=function(){var t=this.props,i=this.target,n=i.style,s=i._gsap,r,o;for(r=0;r<t.length;r+=3)t[r+1]?t[r+1]===2?i[t[r]](t[r+2]):i[t[r]]=t[r+2]:t[r+2]?n[t[r]]=t[r+2]:n.removeProperty(t[r].substr(0,2)==="--"?t[r]:t[r].replace(er,"-$1").toLowerCase());if(this.tfm){for(o in this.tfm)s[o]=this.tfm[o];s.svg&&(s.renderTransform(),i.setAttribute("data-svg-origin",this.svgo||"")),r=tr(),(!r||!r.isStart)&&!n[z]&&(ru(n),s.zOrigin&&n[ht]&&(n[ht]+=" "+s.zOrigin+"px",s.zOrigin=0,s.renderTransform()),s.uncache=1)}},ou=function(t,i){var n={target:t,props:[],revert:cm,save:um};return t._gsap||ft.core.getCache(t),i&&t.style&&t.nodeType&&i.split(",").forEach(function(s){return n.save(s)}),n},au,es=function(t,i){var n=Kt.createElementNS?Kt.createElementNS((i||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),t):Kt.createElement(t);return n&&n.style?n:Kt.createElement(t)},bt=function e(t,i,n){var s=getComputedStyle(t);return s[i]||s.getPropertyValue(i.replace(er,"-$1").toLowerCase())||s.getPropertyValue(i)||!n&&e(t,Ne(i)||i,1)||""},Do="O,Moz,ms,Ms,Webkit".split(","),Ne=function(t,i,n){var s=i||fe,r=s.style,o=5;if(t in r&&!n)return t;for(t=t.charAt(0).toUpperCase()+t.substr(1);o--&&!(Do[o]+t in r););return o<0?null:(o===3?"ms":o>=0?Do[o]:"")+t},is=function(){Zp()&&window.document&&(Ao=window,Kt=Ao.document,Re=Kt.documentElement,fe=es("div")||{style:{}},es("div"),z=Ne(z),ht=z+"Origin",fe.style.cssText="border-width:0;line-height:0;position:absolute;padding:0",au=!!Ne("perspective"),tr=ft.core.reverting,Js=1)},Ro=function(t){var i=t.ownerSVGElement,n=es("svg",i&&i.getAttribute("xmlns")||"http://www.w3.org/2000/svg"),s=t.cloneNode(!0),r;s.style.display="block",n.appendChild(s),Re.appendChild(n);try{r=s.getBBox()}catch{}return n.removeChild(s),Re.removeChild(n),r},Eo=function(t,i){for(var n=i.length;n--;)if(t.hasAttribute(i[n]))return t.getAttribute(i[n])},lu=function(t){var i,n;try{i=t.getBBox()}catch{i=Ro(t),n=1}return i&&(i.width||i.height)||n||(i=Ro(t)),i&&!i.width&&!i.x&&!i.y?{x:+Eo(t,["x","cx","x1"])||0,y:+Eo(t,["y","cy","y1"])||0,width:0,height:0}:i},uu=function(t){return!!(t.getCTM&&(!t.parentNode||t.ownerSVGElement)&&lu(t))},ve=function(t,i){if(i){var n=t.style,s;i in Ut&&i!==ht&&(i=z),n.removeProperty?(s=i.substr(0,2),(s==="ms"||i.substr(0,6)==="webkit")&&(i="-"+i),n.removeProperty(s==="--"?i:i.replace(er,"-$1").toLowerCase())):n.removeAttribute(i)}},Ht=function(t,i,n,s,r,o){var a=new ct(t._pt,i,n,0,1,o?su:nu);return t._pt=a,a.b=s,a.e=r,t._props.push(n),a},Vo={deg:1,rad:1,turn:1},hm={grid:1,flex:1},te=function e(t,i,n,s){var r=parseFloat(n)||0,o=(n+"").trim().substr((r+"").length)||"px",a=fe.style,l=Qp.test(i),c=t.tagName.toLowerCase()==="svg",u=(c?"client":"offset")+(l?"Width":"Height"),h=100,f=s==="px",d=s==="%",m,p,g,y;if(s===o||!r||Vo[s]||Vo[o])return r;if(o!=="px"&&!f&&(r=e(t,i,n,"px")),y=t.getCTM&&uu(t),(d||o==="%")&&(Ut[i]||~i.indexOf("adius")))return m=y?t.getBBox()[l?"width":"height"]:t[u],K(d?r/m*h:r/100*m);if(a[l?"width":"height"]=h+(f?o:s),p=s!=="rem"&&~i.indexOf("adius")||s==="em"&&t.appendChild&&!c?t:t.parentNode,y&&(p=(t.ownerSVGElement||{}).parentNode),(!p||p===Kt||!p.appendChild)&&(p=Kt.body),g=p._gsap,g&&d&&g.width&&l&&g.time===pt.time&&!g.uncache)return K(r/g.width*h);if(d&&(i==="height"||i==="width")){var x=t.style[i];t.style[i]=h+s,m=t[u],x?t.style[i]=x:ve(t,i)}else(d||o==="%")&&!hm[bt(p,"display")]&&(a.position=bt(t,"position")),p===t&&(a.position="static"),p.appendChild(fe),m=fe[u],p.removeChild(fe),a.position="absolute";return l&&d&&(g=me(p),g.time=pt.time,g.width=p[u]),K(f?m*r/h:m&&r?h/m*r:0)},It=function(t,i,n,s){var r;return Js||is(),i in Dt&&i!=="transform"&&(i=Dt[i],~i.indexOf(",")&&(i=i.split(",")[0])),Ut[i]&&i!=="transform"?(r=yi(t,s),r=i!=="transformOrigin"?r[i]:r.svg?r.origin:$i(bt(t,ht))+" "+r.zOrigin+"px"):(r=t.style[i],(!r||r==="auto"||s||~(r+"").indexOf("calc("))&&(r=Xi[i]&&Xi[i](t,i,n)||bt(t,i)||Pl(t,i)||(i==="opacity"?1:0))),n&&!~(r+"").trim().indexOf(" ")?te(t,i,r,n)+n:r},fm=function(t,i,n,s){if(!n||n==="none"){var r=Ne(i,t,1),o=r&&bt(t,r,1);o&&o!==n?(i=r,n=o):i==="borderColor"&&(n=bt(t,"borderTopColor"))}var a=new ct(this._pt,t.style,i,0,1,tu),l=0,c=0,u,h,f,d,m,p,g,y,x,v,w,_;if(a.b=n,a.e=s,n+="",s+="",s.substring(0,6)==="var(--"&&(s=bt(t,s.substring(4,s.indexOf(")")))),s==="auto"&&(p=t.style[i],t.style[i]=s,s=bt(t,i)||s,p?t.style[i]=p:ve(t,i)),u=[n,s],Yl(u),n=u[0],s=u[1],f=n.match(Ce)||[],_=s.match(Ce)||[],_.length){for(;h=Ce.exec(s);)g=h[0],x=s.substring(l,h.index),m?m=(m+1)%5:(x.substr(-5)==="rgba("||x.substr(-5)==="hsla(")&&(m=1),g!==(p=f[c++]||"")&&(d=parseFloat(p)||0,w=p.substr((d+"").length),g.charAt(1)==="="&&(g=De(d,g)+w),y=parseFloat(g),v=g.substr((y+"").length),l=Ce.lastIndex-v.length,v||(v=v||gt.units[i]||w,l===s.length&&(s+=v,a.e+=v)),w!==v&&(d=te(t,i,p,v)||0),a._pt={_next:a._pt,p:x||c===1?x:",",s:d,c:y-d,m:m&&m<4||i==="zIndex"?Math.round:0});a.c=l<s.length?s.substring(l,s.length):""}else a.r=i==="display"&&s==="none"?su:nu;return vl.test(s)&&(a.e=0),this._pt=a,a},ko={top:"0%",bottom:"100%",left:"0%",right:"100%",center:"50%"},dm=function(t){var i=t.split(" "),n=i[0],s=i[1]||"50%";return(n==="top"||n==="bottom"||s==="left"||s==="right")&&(t=n,n=s,s=t),i[0]=ko[n]||n,i[1]=ko[s]||s,i.join(" ")},pm=function(t,i){if(i.tween&&i.tween._time===i.tween._dur){var n=i.t,s=n.style,r=i.u,o=n._gsap,a,l,c;if(r==="all"||r===!0)s.cssText="",l=1;else for(r=r.split(","),c=r.length;--c>-1;)a=r[c],Ut[a]&&(l=1,a=a==="transformOrigin"?ht:z),ve(n,a);l&&(ve(n,z),o&&(o.svg&&n.removeAttribute("transform"),s.scale=s.rotate=s.translate="none",yi(n,1),o.uncache=1,ru(s)))}},Xi={clearProps:function(t,i,n,s,r){if(r.data!=="isFromStart"){var o=t._pt=new ct(t._pt,i,n,0,0,pm);return o.u=s,o.pr=-10,o.tween=r,t._props.push(n),1}}},gi=[1,0,0,1,0,0],cu={},hu=function(t){return t==="matrix(1, 0, 0, 1, 0, 0)"||t==="none"||!t},Oo=function(t){var i=bt(t,z);return hu(i)?gi:i.substr(7).match(_l).map(K)},ir=function(t,i){var n=t._gsap||me(t),s=t.style,r=Oo(t),o,a,l,c;return n.svg&&t.getAttribute("transform")?(l=t.transform.baseVal.consolidate().matrix,r=[l.a,l.b,l.c,l.d,l.e,l.f],r.join(",")==="1,0,0,1,0,0"?gi:r):(r===gi&&!t.offsetParent&&t!==Re&&!n.svg&&(l=s.display,s.display="block",o=t.parentNode,(!o||!t.offsetParent&&!t.getBoundingClientRect().width)&&(c=1,a=t.nextElementSibling,Re.appendChild(t)),r=Oo(t),l?s.display=l:ve(t,"display"),c&&(a?o.insertBefore(t,a):o?o.appendChild(t):Re.removeChild(t))),i&&r.length>6?[r[0],r[1],r[4],r[5],r[12],r[13]]:r)},ns=function(t,i,n,s,r,o){var a=t._gsap,l=r||ir(t,!0),c=a.xOrigin||0,u=a.yOrigin||0,h=a.xOffset||0,f=a.yOffset||0,d=l[0],m=l[1],p=l[2],g=l[3],y=l[4],x=l[5],v=i.split(" "),w=parseFloat(v[0])||0,_=parseFloat(v[1])||0,T,b,S,P;n?l!==gi&&(b=d*g-m*p)&&(S=w*(g/b)+_*(-p/b)+(p*x-g*y)/b,P=w*(-m/b)+_*(d/b)-(d*x-m*y)/b,w=S,_=P):(T=lu(t),w=T.x+(~v[0].indexOf("%")?w/100*T.width:w),_=T.y+(~(v[1]||v[0]).indexOf("%")?_/100*T.height:_)),s||s!==!1&&a.smooth?(y=w-c,x=_-u,a.xOffset=h+(y*d+x*p)-y,a.yOffset=f+(y*m+x*g)-x):a.xOffset=a.yOffset=0,a.xOrigin=w,a.yOrigin=_,a.smooth=!!s,a.origin=i,a.originIsAbsolute=!!n,t.style[ht]="0px 0px",o&&(Ht(o,a,"xOrigin",c,w),Ht(o,a,"yOrigin",u,_),Ht(o,a,"xOffset",h,a.xOffset),Ht(o,a,"yOffset",f,a.yOffset)),t.setAttribute("data-svg-origin",w+" "+_)},yi=function(t,i){var n=t._gsap||new Hl(t);if("x"in n&&!i&&!n.uncache)return n;var s=t.style,r=n.scaleX<0,o="px",a="deg",l=getComputedStyle(t),c=bt(t,ht)||"0",u,h,f,d,m,p,g,y,x,v,w,_,T,b,S,P,D,V,k,O,H,Q,Y,E,X,Wt,Ot,We,ne,nr,Lt,se;return u=h=f=p=g=y=x=v=w=0,d=m=1,n.svg=!!(t.getCTM&&uu(t)),l.translate&&((l.translate!=="none"||l.scale!=="none"||l.rotate!=="none")&&(s[z]=(l.translate!=="none"?"translate3d("+(l.translate+" 0 0").split(" ").slice(0,3).join(", ")+") ":"")+(l.rotate!=="none"?"rotate("+l.rotate+") ":"")+(l.scale!=="none"?"scale("+l.scale.split(" ").join(",")+") ":"")+(l[z]!=="none"?l[z]:"")),s.scale=s.rotate=s.translate="none"),b=ir(t,n.svg),n.svg&&(n.uncache?(X=t.getBBox(),c=n.xOrigin-X.x+"px "+(n.yOrigin-X.y)+"px",E=""):E=!i&&t.getAttribute("data-svg-origin"),ns(t,E||c,!!E||n.originIsAbsolute,n.smooth!==!1,b)),_=n.xOrigin||0,T=n.yOrigin||0,b!==gi&&(V=b[0],k=b[1],O=b[2],H=b[3],u=Q=b[4],h=Y=b[5],b.length===6?(d=Math.sqrt(V*V+k*k),m=Math.sqrt(H*H+O*O),p=V||k?Te(k,V)*ue:0,x=O||H?Te(O,H)*ue+p:0,x&&(m*=Math.abs(Math.cos(x*Ee))),n.svg&&(u-=_-(_*V+T*O),h-=T-(_*k+T*H))):(se=b[6],nr=b[7],Ot=b[8],We=b[9],ne=b[10],Lt=b[11],u=b[12],h=b[13],f=b[14],S=Te(se,ne),g=S*ue,S&&(P=Math.cos(-S),D=Math.sin(-S),E=Q*P+Ot*D,X=Y*P+We*D,Wt=se*P+ne*D,Ot=Q*-D+Ot*P,We=Y*-D+We*P,ne=se*-D+ne*P,Lt=nr*-D+Lt*P,Q=E,Y=X,se=Wt),S=Te(-O,ne),y=S*ue,S&&(P=Math.cos(-S),D=Math.sin(-S),E=V*P-Ot*D,X=k*P-We*D,Wt=O*P-ne*D,Lt=H*D+Lt*P,V=E,k=X,O=Wt),S=Te(k,V),p=S*ue,S&&(P=Math.cos(S),D=Math.sin(S),E=V*P+k*D,X=Q*P+Y*D,k=k*P-V*D,Y=Y*P-Q*D,V=E,Q=X),g&&Math.abs(g)+Math.abs(p)>359.9&&(g=p=0,y=180-y),d=K(Math.sqrt(V*V+k*k+O*O)),m=K(Math.sqrt(Y*Y+se*se)),S=Te(Q,Y),x=Math.abs(S)>2e-4?S*ue:0,w=Lt?1/(Lt<0?-Lt:Lt):0),n.svg&&(E=t.getAttribute("transform"),n.forceCSS=t.setAttribute("transform","")||!hu(bt(t,z)),E&&t.setAttribute("transform",E))),Math.abs(x)>90&&Math.abs(x)<270&&(r?(d*=-1,x+=p<=0?180:-180,p+=p<=0?180:-180):(m*=-1,x+=x<=0?180:-180)),i=i||n.uncache,n.x=u-((n.xPercent=u&&(!i&&n.xPercent||(Math.round(t.offsetWidth/2)===Math.round(-u)?-50:0)))?t.offsetWidth*n.xPercent/100:0)+o,n.y=h-((n.yPercent=h&&(!i&&n.yPercent||(Math.round(t.offsetHeight/2)===Math.round(-h)?-50:0)))?t.offsetHeight*n.yPercent/100:0)+o,n.z=f+o,n.scaleX=K(d),n.scaleY=K(m),n.rotation=K(p)+a,n.rotationX=K(g)+a,n.rotationY=K(y)+a,n.skewX=x+a,n.skewY=v+a,n.transformPerspective=w+o,(n.zOrigin=parseFloat(c.split(" ")[2])||!i&&n.zOrigin||0)&&(s[ht]=$i(c)),n.xOffset=n.yOffset=0,n.force3D=gt.force3D,n.renderTransform=n.svg?gm:au?fu:mm,n.uncache=0,n},$i=function(t){return(t=t.split(" "))[0]+" "+t[1]},wn=function(t,i,n){var s=it(i);return K(parseFloat(i)+parseFloat(te(t,"x",n+"px",s)))+s},mm=function(t,i){i.z="0px",i.rotationY=i.rotationX="0deg",i.force3D=0,fu(t,i)},oe="0deg",$e="0px",ae=") ",fu=function(t,i){var n=i||this,s=n.xPercent,r=n.yPercent,o=n.x,a=n.y,l=n.z,c=n.rotation,u=n.rotationY,h=n.rotationX,f=n.skewX,d=n.skewY,m=n.scaleX,p=n.scaleY,g=n.transformPerspective,y=n.force3D,x=n.target,v=n.zOrigin,w="",_=y==="auto"&&t&&t!==1||y===!0;if(v&&(h!==oe||u!==oe)){var T=parseFloat(u)*Ee,b=Math.sin(T),S=Math.cos(T),P;T=parseFloat(h)*Ee,P=Math.cos(T),o=wn(x,o,b*P*-v),a=wn(x,a,-Math.sin(T)*-v),l=wn(x,l,S*P*-v+v)}g!==$e&&(w+="perspective("+g+ae),(s||r)&&(w+="translate("+s+"%, "+r+"%) "),(_||o!==$e||a!==$e||l!==$e)&&(w+=l!==$e||_?"translate3d("+o+", "+a+", "+l+") ":"translate("+o+", "+a+ae),c!==oe&&(w+="rotate("+c+ae),u!==oe&&(w+="rotateY("+u+ae),h!==oe&&(w+="rotateX("+h+ae),(f!==oe||d!==oe)&&(w+="skew("+f+", "+d+ae),(m!==1||p!==1)&&(w+="scale("+m+", "+p+ae),x.style[z]=w||"translate(0, 0)"},gm=function(t,i){var n=i||this,s=n.xPercent,r=n.yPercent,o=n.x,a=n.y,l=n.rotation,c=n.skewX,u=n.skewY,h=n.scaleX,f=n.scaleY,d=n.target,m=n.xOrigin,p=n.yOrigin,g=n.xOffset,y=n.yOffset,x=n.forceCSS,v=parseFloat(o),w=parseFloat(a),_,T,b,S,P;l=parseFloat(l),c=parseFloat(c),u=parseFloat(u),u&&(u=parseFloat(u),c+=u,l+=u),l||c?(l*=Ee,c*=Ee,_=Math.cos(l)*h,T=Math.sin(l)*h,b=Math.sin(l-c)*-f,S=Math.cos(l-c)*f,c&&(u*=Ee,P=Math.tan(c-u),P=Math.sqrt(1+P*P),b*=P,S*=P,u&&(P=Math.tan(u),P=Math.sqrt(1+P*P),_*=P,T*=P)),_=K(_),T=K(T),b=K(b),S=K(S)):(_=h,S=f,T=b=0),(v&&!~(o+"").indexOf("px")||w&&!~(a+"").indexOf("px"))&&(v=te(d,"x",o,"px"),w=te(d,"y",a,"px")),(m||p||g||y)&&(v=K(v+m-(m*_+p*b)+g),w=K(w+p-(m*T+p*S)+y)),(s||r)&&(P=d.getBBox(),v=K(v+s/100*P.width),w=K(w+r/100*P.height)),P="matrix("+_+","+T+","+b+","+S+","+v+","+w+")",d.setAttribute("transform",P),x&&(d.style[z]=P)},ym=function(t,i,n,s,r){var o=360,a=J(r),l=parseFloat(r)*(a&&~r.indexOf("rad")?ue:1),c=l-s,u=s+c+"deg",h,f;return a&&(h=r.split("_")[1],h==="short"&&(c%=o,c!==c%(o/2)&&(c+=c<0?o:-o)),h==="cw"&&c<0?c=(c+o*Mo)%o-~~(c/o)*o:h==="ccw"&&c>0&&(c=(c-o*Mo)%o-~~(c/o)*o)),t._pt=f=new ct(t._pt,i,n,s,c,tm),f.e=u,f.u="deg",t._props.push(n),f},Lo=function(t,i){for(var n in i)t[n]=i[n];return t},_m=function(t,i,n){var s=Lo({},n._gsap),r="perspective,force3D,transformOrigin,svgOrigin",o=n.style,a,l,c,u,h,f,d,m;s.svg?(c=n.getAttribute("transform"),n.setAttribute("transform",""),o[z]=i,a=yi(n,1),ve(n,z),n.setAttribute("transform",c)):(c=getComputedStyle(n)[z],o[z]=i,a=yi(n,1),o[z]=c);for(l in Ut)c=s[l],u=a[l],c!==u&&r.indexOf(l)<0&&(d=it(c),m=it(u),h=d!==m?te(n,l,c,m):parseFloat(c),f=parseFloat(u),t._pt=new ct(t._pt,a,l,h,f-h,ts),t._pt.u=m||0,t._props.push(l));Lo(a,s)};ut("padding,margin,Width,Radius",function(e,t){var i="Top",n="Right",s="Bottom",r="Left",o=(t<3?[i,n,s,r]:[i+r,i+n,s+n,s+r]).map(function(a){return t<2?e+a:"border"+a+e});Xi[t>1?"border"+e:e]=function(a,l,c,u,h){var f,d;if(arguments.length<4)return f=o.map(function(m){return It(a,m,c)}),d=f.join(" "),d.split(f[0]).length===5?f[0]:d;f=(u+"").split(" "),d={},o.forEach(function(m,p){return d[m]=f[p]=f[p]||f[(p-1)/2|0]}),a.init(l,d,h)}});var du={name:"css",register:is,targetTest:function(t){return t.style&&t.nodeType},init:function(t,i,n,s,r){var o=this._props,a=t.style,l=n.vars.startAt,c,u,h,f,d,m,p,g,y,x,v,w,_,T,b,S;Js||is(),this.styles=this.styles||ou(t),S=this.styles.props,this.tween=n;for(p in i)if(p!=="autoRound"&&(u=i[p],!(dt[p]&&Gl(p,i,n,s,t,r)))){if(d=typeof u,m=Xi[p],d==="function"&&(u=u.call(n,s,t,r),d=typeof u),d==="string"&&~u.indexOf("random(")&&(u=di(u)),m)m(this,t,p,u,n)&&(b=1);else if(p.substr(0,2)==="--")c=(getComputedStyle(t).getPropertyValue(p)+"").trim(),u+="",qt.lastIndex=0,qt.test(c)||(g=it(c),y=it(u)),y?g!==y&&(c=te(t,p,c,y)+y):g&&(u+=g),this.add(a,"setProperty",c,u,s,r,0,0,p),o.push(p),S.push(p,0,a[p]);else if(d!=="undefined"){if(l&&p in l?(c=typeof l[p]=="function"?l[p].call(n,s,t,r):l[p],J(c)&&~c.indexOf("random(")&&(c=di(c)),it(c+"")||c==="auto"||(c+=gt.units[p]||it(It(t,p))||""),(c+"").charAt(1)==="="&&(c=It(t,p))):c=It(t,p),f=parseFloat(c),x=d==="string"&&u.charAt(1)==="="&&u.substr(0,2),x&&(u=u.substr(2)),h=parseFloat(u),p in Dt&&(p==="autoAlpha"&&(f===1&&It(t,"visibility")==="hidden"&&h&&(f=0),S.push("visibility",0,a.visibility),Ht(this,a,"visibility",f?"inherit":"hidden",h?"inherit":"hidden",!h)),p!=="scale"&&p!=="transform"&&(p=Dt[p],~p.indexOf(",")&&(p=p.split(",")[0]))),v=p in Ut,v){if(this.styles.save(p),d==="string"&&u.substring(0,6)==="var(--"&&(u=bt(t,u.substring(4,u.indexOf(")"))),h=parseFloat(u)),w||(_=t._gsap,_.renderTransform&&!i.parseTransform||yi(t,i.parseTransform),T=i.smoothOrigin!==!1&&_.smooth,w=this._pt=new ct(this._pt,a,z,0,1,_.renderTransform,_,0,-1),w.dep=1),p==="scale")this._pt=new ct(this._pt,_,"scaleY",_.scaleY,(x?De(_.scaleY,x+h):h)-_.scaleY||0,ts),this._pt.u=0,o.push("scaleY",p),p+="X";else if(p==="transformOrigin"){S.push(ht,0,a[ht]),u=dm(u),_.svg?ns(t,u,0,T,0,this):(y=parseFloat(u.split(" ")[2])||0,y!==_.zOrigin&&Ht(this,_,"zOrigin",_.zOrigin,y),Ht(this,a,p,$i(c),$i(u)));continue}else if(p==="svgOrigin"){ns(t,u,1,T,0,this);continue}else if(p in cu){ym(this,_,p,f,x?De(f,x+u):u);continue}else if(p==="smoothOrigin"){Ht(this,_,"smooth",_.smooth,u);continue}else if(p==="force3D"){_[p]=u;continue}else if(p==="transform"){_m(this,u,t);continue}}else p in a||(p=Ne(p)||p);if(v||(h||h===0)&&(f||f===0)&&!Jp.test(u)&&p in a)g=(c+"").substr((f+"").length),h||(h=0),y=it(u)||(p in gt.units?gt.units[p]:g),g!==y&&(f=te(t,p,c,y)),this._pt=new ct(this._pt,v?_:a,p,f,(x?De(f,x+h):h)-f,!v&&(y==="px"||p==="zIndex")&&i.autoRound!==!1?im:ts),this._pt.u=y||0,g!==y&&y!=="%"&&(this._pt.b=c,this._pt.r=em);else if(p in a)fm.call(this,t,p,c,x?x+u:u);else if(p in t)this.add(t,p,c||t[p],x?x+u:u,s,r);else if(p!=="parseTransform"){Ws(p,u);continue}v||(p in a?S.push(p,0,a[p]):typeof t[p]=="function"?S.push(p,2,t[p]()):S.push(p,1,c||t[p])),o.push(p)}}b&&eu(this)},render:function(t,i){if(i.tween._time||!tr())for(var n=i._pt;n;)n.r(t,n.d),n=n._next;else i.styles.revert()},get:It,aliases:Dt,getSetter:function(t,i,n){var s=Dt[i];return s&&s.indexOf(",")<0&&(i=s),i in Ut&&i!==ht&&(t._gsap.x||It(t,"x"))?n&&Co===n?i==="scale"?om:rm:(Co=n||{})&&(i==="scale"?am:lm):t.style&&!zs(t.style[i])?nm:~i.indexOf("-")?sm:Zs(t,i)},core:{_removeProperty:ve,_getMatrix:ir}};ft.utils.checkPrefix=Ne;ft.core.getStyleSaver=ou;(function(e,t,i,n){var s=ut(e+","+t+","+i,function(r){Ut[r]=1});ut(t,function(r){gt.units[r]="deg",cu[r]=1}),Dt[s[13]]=e+","+t,ut(n,function(r){var o=r.split(":");Dt[o[1]]=s[o[0]]})})("x,y,z,scale,scaleX,scaleY,xPercent,yPercent","rotation,rotationX,rotationY,skewX,skewY","transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective","0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY");ut("x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective",function(e){gt.units[e]="px"});ft.registerPlugin(du);var vm=ft.registerPlugin(du)||ft;vm.core.Tween;var xm="1.3.4";function pu(e,t,i){return Math.max(e,Math.min(t,i))}function Tm(e,t,i){return(1-i)*e+i*t}function wm(e,t,i,n){return Tm(e,t,1-Math.exp(-i*n))}function Sm(e,t){return(e%t+t)%t}var bm=class{constructor(){C(this,"isRunning",!1);C(this,"value",0);C(this,"from",0);C(this,"to",0);C(this,"currentTime",0);C(this,"lerp");C(this,"duration");C(this,"easing");C(this,"onUpdate")}advance(e){var i;if(!this.isRunning)return;let t=!1;if(this.duration&&this.easing){this.currentTime+=e;const n=pu(0,this.currentTime/this.duration,1);t=n>=1;const s=t?1:this.easing(n);this.value=this.from+(this.to-this.from)*s}else this.lerp?(this.value=wm(this.value,this.to,this.lerp*60,e),Math.round(this.value)===this.to&&(this.value=this.to,t=!0)):(this.value=this.to,t=!0);t&&this.stop(),(i=this.onUpdate)==null||i.call(this,this.value,t)}stop(){this.isRunning=!1}fromTo(e,t,{lerp:i,duration:n,easing:s,onStart:r,onUpdate:o}){this.from=this.value=e,this.to=t,this.lerp=i,this.duration=n,this.easing=s,this.currentTime=0,this.isRunning=!0,r==null||r(),this.onUpdate=o}};function Pm(e,t){let i;return function(...n){let s=this;clearTimeout(i),i=setTimeout(()=>{i=void 0,e.apply(s,n)},t)}}var Am=class{constructor(e,t,{autoResize:i=!0,debounce:n=250}={}){C(this,"width",0);C(this,"height",0);C(this,"scrollHeight",0);C(this,"scrollWidth",0);C(this,"debouncedResize");C(this,"wrapperResizeObserver");C(this,"contentResizeObserver");C(this,"resize",()=>{this.onWrapperResize(),this.onContentResize()});C(this,"onWrapperResize",()=>{this.wrapper instanceof Window?(this.width=window.innerWidth,this.height=window.innerHeight):(this.width=this.wrapper.clientWidth,this.height=this.wrapper.clientHeight)});C(this,"onContentResize",()=>{this.wrapper instanceof Window?(this.scrollHeight=this.content.scrollHeight,this.scrollWidth=this.content.scrollWidth):(this.scrollHeight=this.wrapper.scrollHeight,this.scrollWidth=this.wrapper.scrollWidth)});this.wrapper=e,this.content=t,i&&(this.debouncedResize=Pm(this.resize,n),this.wrapper instanceof Window?window.addEventListener("resize",this.debouncedResize,!1):(this.wrapperResizeObserver=new ResizeObserver(this.debouncedResize),this.wrapperResizeObserver.observe(this.wrapper)),this.contentResizeObserver=new ResizeObserver(this.debouncedResize),this.contentResizeObserver.observe(this.content)),this.resize()}destroy(){var e,t;(e=this.wrapperResizeObserver)==null||e.disconnect(),(t=this.contentResizeObserver)==null||t.disconnect(),this.wrapper===window&&this.debouncedResize&&window.removeEventListener("resize",this.debouncedResize,!1)}get limit(){return{x:this.scrollWidth-this.width,y:this.scrollHeight-this.height}}},mu=class{constructor(){C(this,"events",{})}emit(e,...t){var n;let i=this.events[e]||[];for(let s=0,r=i.length;s<r;s++)(n=i[s])==null||n.call(i,...t)}on(e,t){var i;return(i=this.events[e])!=null&&i.push(t)||(this.events[e]=[t]),()=>{var n;this.events[e]=(n=this.events[e])==null?void 0:n.filter(s=>t!==s)}}off(e,t){var i;this.events[e]=(i=this.events[e])==null?void 0:i.filter(n=>t!==n)}destroy(){this.events={}}},Fo=100/6,Yt={passive:!1},Cm=class{constructor(e,t={wheelMultiplier:1,touchMultiplier:1}){C(this,"touchStart",{x:0,y:0});C(this,"lastDelta",{x:0,y:0});C(this,"window",{width:0,height:0});C(this,"emitter",new mu);C(this,"onTouchStart",e=>{const{clientX:t,clientY:i}=e.targetTouches?e.targetTouches[0]:e;this.touchStart.x=t,this.touchStart.y=i,this.lastDelta={x:0,y:0},this.emitter.emit("scroll",{deltaX:0,deltaY:0,event:e})});C(this,"onTouchMove",e=>{const{clientX:t,clientY:i}=e.targetTouches?e.targetTouches[0]:e,n=-(t-this.touchStart.x)*this.options.touchMultiplier,s=-(i-this.touchStart.y)*this.options.touchMultiplier;this.touchStart.x=t,this.touchStart.y=i,this.lastDelta={x:n,y:s},this.emitter.emit("scroll",{deltaX:n,deltaY:s,event:e})});C(this,"onTouchEnd",e=>{this.emitter.emit("scroll",{deltaX:this.lastDelta.x,deltaY:this.lastDelta.y,event:e})});C(this,"onWheel",e=>{let{deltaX:t,deltaY:i,deltaMode:n}=e;const s=n===1?Fo:n===2?this.window.width:1,r=n===1?Fo:n===2?this.window.height:1;t*=s,i*=r,t*=this.options.wheelMultiplier,i*=this.options.wheelMultiplier,this.emitter.emit("scroll",{deltaX:t,deltaY:i,event:e})});C(this,"onWindowResize",()=>{this.window={width:window.innerWidth,height:window.innerHeight}});this.element=e,this.options=t,window.addEventListener("resize",this.onWindowResize,!1),this.onWindowResize(),this.element.addEventListener("wheel",this.onWheel,Yt),this.element.addEventListener("touchstart",this.onTouchStart,Yt),this.element.addEventListener("touchmove",this.onTouchMove,Yt),this.element.addEventListener("touchend",this.onTouchEnd,Yt)}on(e,t){return this.emitter.on(e,t)}destroy(){this.emitter.destroy(),window.removeEventListener("resize",this.onWindowResize,!1),this.element.removeEventListener("wheel",this.onWheel,Yt),this.element.removeEventListener("touchstart",this.onTouchStart,Yt),this.element.removeEventListener("touchmove",this.onTouchMove,Yt),this.element.removeEventListener("touchend",this.onTouchEnd,Yt)}},Io=e=>Math.min(1,1.001-Math.pow(2,-10*e)),Om=class{constructor({wrapper:e=window,content:t=document.documentElement,eventsTarget:i=e,smoothWheel:n=!0,syncTouch:s=!1,syncTouchLerp:r=.075,touchInertiaMultiplier:o=35,duration:a,easing:l,lerp:c=.1,infinite:u=!1,orientation:h="vertical",gestureOrientation:f="vertical",touchMultiplier:d=1,wheelMultiplier:m=1,autoResize:p=!0,prevent:g,virtualScroll:y,overscroll:x=!0,autoRaf:v=!1,anchors:w=!1,autoToggle:_=!1,allowNestedScroll:T=!1,__experimental__naiveDimensions:b=!1}={}){C(this,"_isScrolling",!1);C(this,"_isStopped",!1);C(this,"_isLocked",!1);C(this,"_preventNextNativeScrollEvent",!1);C(this,"_resetVelocityTimeout",null);C(this,"__rafID",null);C(this,"isTouching");C(this,"time",0);C(this,"userData",{});C(this,"lastVelocity",0);C(this,"velocity",0);C(this,"direction",0);C(this,"options");C(this,"targetScroll");C(this,"animatedScroll");C(this,"animate",new bm);C(this,"emitter",new mu);C(this,"dimensions");C(this,"virtualScroll");C(this,"onScrollEnd",e=>{e instanceof CustomEvent||(this.isScrolling==="smooth"||this.isScrolling===!1)&&e.stopPropagation()});C(this,"dispatchScrollendEvent",()=>{this.options.wrapper.dispatchEvent(new CustomEvent("scrollend",{bubbles:this.options.wrapper===window,detail:{lenisScrollEnd:!0}}))});C(this,"onTransitionEnd",e=>{if(e.propertyName.includes("overflow")){const t=this.isHorizontal?"overflow-x":"overflow-y",i=getComputedStyle(this.rootElement)[t];["hidden","clip"].includes(i)?this.stop():this.start()}});C(this,"onClick",e=>{const i=e.composedPath().find(n=>{var s,r,o;return n instanceof HTMLAnchorElement&&(((s=n.getAttribute("href"))==null?void 0:s.startsWith("#"))||((r=n.getAttribute("href"))==null?void 0:r.startsWith("/#"))||((o=n.getAttribute("href"))==null?void 0:o.startsWith("./#")))});if(i){const n=i.getAttribute("href");if(n){const s=typeof this.options.anchors=="object"&&this.options.anchors?this.options.anchors:void 0;let r=`#${n.split("#")[1]}`;["#","/#","./#","#top","/#top","./#top"].includes(n)&&(r=0),this.scrollTo(r,s)}}});C(this,"onPointerDown",e=>{e.button===1&&this.reset()});C(this,"onVirtualScroll",e=>{if(typeof this.options.virtualScroll=="function"&&this.options.virtualScroll(e)===!1)return;const{deltaX:t,deltaY:i,event:n}=e;if(this.emitter.emit("virtual-scroll",{deltaX:t,deltaY:i,event:n}),n.ctrlKey||n.lenisStopPropagation)return;const s=n.type.includes("touch"),r=n.type.includes("wheel");this.isTouching=n.type==="touchstart"||n.type==="touchmove";const o=t===0&&i===0;if(this.options.syncTouch&&s&&n.type==="touchstart"&&o&&!this.isStopped&&!this.isLocked){this.reset();return}const l=this.options.gestureOrientation==="vertical"&&i===0||this.options.gestureOrientation==="horizontal"&&t===0;if(o||l)return;let c=n.composedPath();c=c.slice(0,c.indexOf(this.rootElement));const u=this.options.prevent;if(c.find(g=>{var y,x,v;return g instanceof HTMLElement&&(typeof u=="function"&&(u==null?void 0:u(g))||((y=g.hasAttribute)==null?void 0:y.call(g,"data-lenis-prevent"))||s&&((x=g.hasAttribute)==null?void 0:x.call(g,"data-lenis-prevent-touch"))||r&&((v=g.hasAttribute)==null?void 0:v.call(g,"data-lenis-prevent-wheel"))||this.options.allowNestedScroll&&this.checkNestedScroll(g,{deltaX:t,deltaY:i}))}))return;if(this.isStopped||this.isLocked){n.preventDefault();return}if(!(this.options.syncTouch&&s||this.options.smoothWheel&&r)){this.isScrolling="native",this.animate.stop(),n.lenisStopPropagation=!0;return}let f=i;this.options.gestureOrientation==="both"?f=Math.abs(i)>Math.abs(t)?i:t:this.options.gestureOrientation==="horizontal"&&(f=t),(!this.options.overscroll||this.options.infinite||this.options.wrapper!==window&&(this.animatedScroll>0&&this.animatedScroll<this.limit||this.animatedScroll===0&&i>0||this.animatedScroll===this.limit&&i<0))&&(n.lenisStopPropagation=!0),n.preventDefault();const d=s&&this.options.syncTouch,p=s&&n.type==="touchend"&&Math.abs(f)>5;p&&(f=this.velocity*this.options.touchInertiaMultiplier),this.scrollTo(this.targetScroll+f,{programmatic:!1,...d?{lerp:p?this.options.syncTouchLerp:1}:{lerp:this.options.lerp,duration:this.options.duration,easing:this.options.easing}})});C(this,"onNativeScroll",()=>{if(this._resetVelocityTimeout!==null&&(clearTimeout(this._resetVelocityTimeout),this._resetVelocityTimeout=null),this._preventNextNativeScrollEvent){this._preventNextNativeScrollEvent=!1;return}if(this.isScrolling===!1||this.isScrolling==="native"){const e=this.animatedScroll;this.animatedScroll=this.targetScroll=this.actualScroll,this.lastVelocity=this.velocity,this.velocity=this.animatedScroll-e,this.direction=Math.sign(this.animatedScroll-e),this.isStopped||(this.isScrolling="native"),this.emit(),this.velocity!==0&&(this._resetVelocityTimeout=setTimeout(()=>{this.lastVelocity=this.velocity,this.velocity=0,this.isScrolling=!1,this.emit()},400))}});C(this,"raf",e=>{const t=e-(this.time||e);this.time=e,this.animate.advance(t*.001),this.options.autoRaf&&(this.__rafID=requestAnimationFrame(this.raf))});window.lenisVersion=xm,(!e||e===document.documentElement)&&(e=window),typeof a=="number"&&typeof l!="function"?l=Io:typeof l=="function"&&typeof a!="number"&&(a=1),this.options={wrapper:e,content:t,eventsTarget:i,smoothWheel:n,syncTouch:s,syncTouchLerp:r,touchInertiaMultiplier:o,duration:a,easing:l,lerp:c,infinite:u,gestureOrientation:f,orientation:h,touchMultiplier:d,wheelMultiplier:m,autoResize:p,prevent:g,virtualScroll:y,overscroll:x,autoRaf:v,anchors:w,autoToggle:_,allowNestedScroll:T,__experimental__naiveDimensions:b},this.dimensions=new Am(e,t,{autoResize:p}),this.updateClassName(),this.targetScroll=this.animatedScroll=this.actualScroll,this.options.wrapper.addEventListener("scroll",this.onNativeScroll,!1),this.options.wrapper.addEventListener("scrollend",this.onScrollEnd,{capture:!0}),this.options.anchors&&this.options.wrapper===window&&this.options.wrapper.addEventListener("click",this.onClick,!1),this.options.wrapper.addEventListener("pointerdown",this.onPointerDown,!1),this.virtualScroll=new Cm(i,{touchMultiplier:d,wheelMultiplier:m}),this.virtualScroll.on("scroll",this.onVirtualScroll),this.options.autoToggle&&this.rootElement.addEventListener("transitionend",this.onTransitionEnd,{passive:!0}),this.options.autoRaf&&(this.__rafID=requestAnimationFrame(this.raf))}destroy(){this.emitter.destroy(),this.options.wrapper.removeEventListener("scroll",this.onNativeScroll,!1),this.options.wrapper.removeEventListener("scrollend",this.onScrollEnd,{capture:!0}),this.options.wrapper.removeEventListener("pointerdown",this.onPointerDown,!1),this.options.anchors&&this.options.wrapper===window&&this.options.wrapper.removeEventListener("click",this.onClick,!1),this.virtualScroll.destroy(),this.dimensions.destroy(),this.cleanUpClassName(),this.__rafID&&cancelAnimationFrame(this.__rafID)}on(e,t){return this.emitter.on(e,t)}off(e,t){return this.emitter.off(e,t)}setScroll(e){this.isHorizontal?this.options.wrapper.scrollTo({left:e,behavior:"instant"}):this.options.wrapper.scrollTo({top:e,behavior:"instant"})}resize(){this.dimensions.resize(),this.animatedScroll=this.targetScroll=this.actualScroll,this.emit()}emit(){this.emitter.emit("scroll",this)}reset(){this.isLocked=!1,this.isScrolling=!1,this.animatedScroll=this.targetScroll=this.actualScroll,this.lastVelocity=this.velocity=0,this.animate.stop()}start(){this.isStopped&&(this.reset(),this.isStopped=!1,this.emit())}stop(){this.isStopped||(this.reset(),this.isStopped=!0,this.emit())}scrollTo(e,{offset:t=0,immediate:i=!1,lock:n=!1,duration:s=this.options.duration,easing:r=this.options.easing,lerp:o=this.options.lerp,onStart:a,onComplete:l,force:c=!1,programmatic:u=!0,userData:h}={}){if(!((this.isStopped||this.isLocked)&&!c)){if(typeof e=="string"&&["top","left","start"].includes(e))e=0;else if(typeof e=="string"&&["bottom","right","end"].includes(e))e=this.limit;else{let f;if(typeof e=="string"?f=document.querySelector(e):e instanceof HTMLElement&&(e!=null&&e.nodeType)&&(f=e),f){if(this.options.wrapper!==window){const m=this.rootElement.getBoundingClientRect();t-=this.isHorizontal?m.left:m.top}const d=f.getBoundingClientRect();e=(this.isHorizontal?d.left:d.top)+this.animatedScroll}}if(typeof e=="number"){if(e+=t,e=Math.round(e),this.options.infinite){if(u){this.targetScroll=this.animatedScroll=this.scroll;const f=e-this.animatedScroll;f>this.limit/2?e=e-this.limit:f<-this.limit/2&&(e=e+this.limit)}}else e=pu(0,e,this.limit);if(e===this.targetScroll){a==null||a(this),l==null||l(this);return}if(this.userData=h??{},i){this.animatedScroll=this.targetScroll=e,this.setScroll(this.scroll),this.reset(),this.preventNextNativeScrollEvent(),this.emit(),l==null||l(this),this.userData={},requestAnimationFrame(()=>{this.dispatchScrollendEvent()});return}u||(this.targetScroll=e),typeof s=="number"&&typeof r!="function"?r=Io:typeof r=="function"&&typeof s!="number"&&(s=1),this.animate.fromTo(this.animatedScroll,e,{duration:s,easing:r,lerp:o,onStart:()=>{n&&(this.isLocked=!0),this.isScrolling="smooth",a==null||a(this)},onUpdate:(f,d)=>{this.isScrolling="smooth",this.lastVelocity=this.velocity,this.velocity=f-this.animatedScroll,this.direction=Math.sign(this.velocity),this.animatedScroll=f,this.setScroll(this.scroll),u&&(this.targetScroll=f),d||this.emit(),d&&(this.reset(),this.emit(),l==null||l(this),this.userData={},requestAnimationFrame(()=>{this.dispatchScrollendEvent()}),this.preventNextNativeScrollEvent())}})}}}preventNextNativeScrollEvent(){this._preventNextNativeScrollEvent=!0,requestAnimationFrame(()=>{this._preventNextNativeScrollEvent=!1})}checkNestedScroll(e,{deltaX:t,deltaY:i}){const n=Date.now(),s=e._lenis??(e._lenis={});let r,o,a,l,c,u,h,f;const d=this.options.gestureOrientation;if(n-(s.time??0)>2e3){s.time=Date.now();const _=window.getComputedStyle(e);s.computedStyle=_;const T=_.overflowX,b=_.overflowY;if(r=["auto","overlay","scroll"].includes(T),o=["auto","overlay","scroll"].includes(b),s.hasOverflowX=r,s.hasOverflowY=o,!r&&!o||d==="vertical"&&!o||d==="horizontal"&&!r)return!1;c=e.scrollWidth,u=e.scrollHeight,h=e.clientWidth,f=e.clientHeight,a=c>h,l=u>f,s.isScrollableX=a,s.isScrollableY=l,s.scrollWidth=c,s.scrollHeight=u,s.clientWidth=h,s.clientHeight=f}else a=s.isScrollableX,l=s.isScrollableY,r=s.hasOverflowX,o=s.hasOverflowY,c=s.scrollWidth,u=s.scrollHeight,h=s.clientWidth,f=s.clientHeight;if(!r&&!o||!a&&!l||d==="vertical"&&(!o||!l)||d==="horizontal"&&(!r||!a))return!1;let m;if(d==="horizontal")m="x";else if(d==="vertical")m="y";else{const _=t!==0,T=i!==0;_&&r&&a&&(m="x"),T&&o&&l&&(m="y")}if(!m)return!1;let p,g,y,x,v;if(m==="x")p=e.scrollLeft,g=c-h,y=t,x=r,v=a;else if(m==="y")p=e.scrollTop,g=u-f,y=i,x=o,v=l;else return!1;return(y>0?p<g:p>0)&&x&&v}get rootElement(){return this.options.wrapper===window?document.documentElement:this.options.wrapper}get limit(){return this.options.__experimental__naiveDimensions?this.isHorizontal?this.rootElement.scrollWidth-this.rootElement.clientWidth:this.rootElement.scrollHeight-this.rootElement.clientHeight:this.dimensions.limit[this.isHorizontal?"x":"y"]}get isHorizontal(){return this.options.orientation==="horizontal"}get actualScroll(){const e=this.options.wrapper;return this.isHorizontal?e.scrollX??e.scrollLeft:e.scrollY??e.scrollTop}get scroll(){return this.options.infinite?Sm(this.animatedScroll,this.limit):this.animatedScroll}get progress(){return this.limit===0?1:this.scroll/this.limit}get isScrolling(){return this._isScrolling}set isScrolling(e){this._isScrolling!==e&&(this._isScrolling=e,this.updateClassName())}get isStopped(){return this._isStopped}set isStopped(e){this._isStopped!==e&&(this._isStopped=e,this.updateClassName())}get isLocked(){return this._isLocked}set isLocked(e){this._isLocked!==e&&(this._isLocked=e,this.updateClassName())}get isSmooth(){return this.isScrolling==="smooth"}get className(){let e="lenis";return this.options.autoToggle&&(e+=" lenis-autoToggle"),this.isStopped&&(e+=" lenis-stopped"),this.isLocked&&(e+=" lenis-locked"),this.isScrolling&&(e+=" lenis-scrolling"),this.isScrolling==="smooth"&&(e+=" lenis-smooth"),e}updateClassName(){this.cleanUpClassName(),this.rootElement.className=`${this.rootElement.className} ${this.className}`.trim()}cleanUpClassName(){this.rootElement.className=this.rootElement.className.replace(/lenis(-\w+)?/g,"").trim()}};export{Em as A,Om as L,Rm as R,vm as g,Bt as j,km as m,A as r};
