{"hash": "b6b19c05", "configHash": "791a77cf", "lockfileHash": "ff05268c", "browserHash": "558dc14c", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "5517c798", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "2c727387", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "a8d10f02", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "ec4435e5", "needsInterop": true}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "fc0f088d", "needsInterop": false}, "gsap": {"src": "../../gsap/index.js", "file": "gsap.js", "fileHash": "1bff2667", "needsInterop": false}, "gsap/ScrollTrigger": {"src": "../../gsap/ScrollTrigger.js", "file": "gsap_ScrollTrigger.js", "fileHash": "4cfd5c9d", "needsInterop": false}, "lenis": {"src": "../../lenis/dist/lenis.mjs", "file": "lenis.js", "fileHash": "fe04a6b6", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "93110c4a", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "7c017c0c", "needsInterop": true}}, "chunks": {"chunk-KDCVS43I": {"file": "chunk-KDCVS43I.js"}, "chunk-S725DACQ": {"file": "chunk-S725DACQ.js"}, "chunk-RLJ2RCJQ": {"file": "chunk-RLJ2RCJQ.js"}, "chunk-DC5AMYBS": {"file": "chunk-DC5AMYBS.js"}}}