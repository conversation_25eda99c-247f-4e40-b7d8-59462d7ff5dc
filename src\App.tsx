import React, { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Header from './components/Header'
import Hero from './components/Hero'
import FeaturedCollections from './components/FeaturedCollections'
import About from './components/About'
import ProductShowcase from './components/ProductShowcase'
import Contact from './components/Contact'
import Footer from './components/Footer'
import LoadingScreen from './components/LoadingScreen'
import { useSmoothScroll } from './hooks/useSmoothScroll'
import './App.css'

function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [showContent, setShowContent] = useState(false);
  const { scrollTo } = useSmoothScroll();

  useEffect(() => {
    // Smooth scrolling for anchor links
    const handleClick = (e: Event) => {
      const target = e.target as HTMLAnchorElement;
      if (target.hash) {
        e.preventDefault();
        const targetId = target.hash.slice(1);
        scrollTo(`#${targetId}`, { offset: -80 });
      }
    };

    document.addEventListener('click', handleClick);
    return () => document.removeEventListener('click', handleClick);
  }, [scrollTo]);

  const handleLoadingComplete = () => {
    setIsLoading(false);
    setTimeout(() => setShowContent(true), 300);
  };

  return (
    <>
      <LoadingScreen onComplete={handleLoadingComplete} />

      <AnimatePresence>
        {showContent && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, ease: 'power2.out' }}
            className="min-h-screen bg-white"
          >
            <Header />
            <main>
              <Hero />
              <FeaturedCollections />
              <About />
              <ProductShowcase />
              <Contact />
            </main>
            <Footer />
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}

export default App
