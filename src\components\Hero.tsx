import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowDown, Sparkles, Star } from 'lucide-react';
import { useParallax, useRevealAnimation } from '../hooks/useParallax';
import { luxuryImages } from '../data/luxuryImages';

const Hero: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const parallaxRef = useParallax(0.3);
  const contentRef = useRevealAnimation(0.2);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image with Parallax */}
      <div
        ref={parallaxRef}
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `linear-gradient(rgba(253, 242, 248, 0.8), rgba(252, 231, 243, 0.9)), url(${luxuryImages.hero.background})`,
        }}
      />

      {/* Luxury Background Elements */}
      <div className="absolute inset-0">
        {/* Elegant geometric shapes with luxury colors */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3]
          }}
          transition={{ duration: 4, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-20 left-10 w-32 h-32 bg-luxury-300/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.2, 0.5, 0.2]
          }}
          transition={{ duration: 5, repeat: Infinity, ease: 'easeInOut', delay: 1 }}
          className="absolute bottom-20 right-10 w-40 h-40 bg-rosegold-200/20 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.1, 0.4, 0.1]
          }}
          transition={{ duration: 6, repeat: Infinity, ease: 'easeInOut', delay: 2 }}
          className="absolute top-1/2 left-1/3 w-24 h-24 bg-champagne-400/20 rounded-full blur-2xl"
        />

        {/* Floating luxury elements */}
        <motion.div
          animate={{
            y: [-20, 20, -20],
            rotate: [0, 5, 0]
          }}
          transition={{ duration: 6, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-1/4 left-1/4"
        >
          <Sparkles className="w-6 h-6 text-luxury-400/60" />
        </motion.div>
        <motion.div
          animate={{
            y: [20, -20, 20],
            rotate: [0, -5, 0]
          }}
          transition={{ duration: 7, repeat: Infinity, ease: 'easeInOut', delay: 1 }}
          className="absolute top-3/4 right-1/4"
        >
          <Star className="w-4 h-4 text-rosegold-400/50" />
        </motion.div>
        <motion.div
          animate={{
            y: [-15, 15, -15],
            rotate: [0, 10, 0]
          }}
          transition={{ duration: 8, repeat: Infinity, ease: 'easeInOut', delay: 2 }}
          className="absolute top-1/2 right-1/3"
        >
          <Sparkles className="w-5 h-5 text-champagne-300/70" />
        </motion.div>
      </div>

      {/* Main Content */}
      <div ref={contentRef} className="relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto">

        {/* Luxury Badge */}
        <motion.div
          initial={{ opacity: 0, y: 20, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3, ease: 'power2.out' }}
          className="inline-flex items-center px-8 py-3 mb-8 glass-effect rounded-full border border-luxury-200/50 shadow-luxury"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 8, repeat: Infinity, ease: 'linear' }}
          >
            <Sparkles className="w-5 h-5 text-luxury-600 mr-3" />
          </motion.div>
          <span className="text-sm font-semibold text-gray-800 tracking-widest">
            EXCLUSIVE LUXURY COLLECTION
          </span>
        </motion.div>

        {/* Main Headlines */}
        <motion.h1
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.5, ease: 'power2.out' }}
          className="text-5xl sm:text-6xl lg:text-7xl xl:text-9xl font-bold mb-8"
        >
          <motion.span
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 1, delay: 0.7, ease: 'power2.out' }}
            className="block text-gray-900 font-serif leading-tight"
          >
            Elegance
          </motion.span>
          <motion.span
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 1, delay: 0.9, ease: 'power2.out' }}
            className="block luxury-text-gradient font-script leading-tight"
          >
            Redefined
          </motion.span>
        </motion.h1>

        <motion.p
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 1.1, ease: 'power2.out' }}
          className="text-xl sm:text-2xl text-gray-700 mb-12 max-w-4xl mx-auto leading-relaxed font-luxury"
        >
          Discover our curated collection of exquisite women's fashion,
          where timeless sophistication meets contemporary luxury.
        </motion.p>

        {/* CTA Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 1.3, ease: 'power2.out' }}
          className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16"
        >
          <motion.button
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.98 }}
            className="group luxury-button shadow-luxury-lg"
          >
            <span className="flex items-center">
              Explore Collection
              <motion.div
                animate={{ y: [0, 3, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
              >
                <ArrowDown className="ml-3 w-5 h-5" />
              </motion.div>
            </span>
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.98 }}
            className="px-8 py-4 glass-effect text-gray-800 rounded-full font-semibold tracking-wide border border-luxury-200/50 hover:border-luxury-400/50 hover:shadow-luxury transition-all duration-300"
          >
            Book Consultation
          </motion.button>
        </motion.div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 1.5, ease: 'power2.out' }}
          className="flex flex-col items-center"
        >
          <span className="text-sm text-gray-600 mb-6 tracking-widest font-medium">DISCOVER MORE</span>
          <motion.div
            animate={{ height: [40, 60, 40] }}
            transition={{ duration: 3, repeat: Infinity, ease: 'easeInOut' }}
            className="w-px bg-gradient-to-b from-luxury-400 via-luxury-300 to-transparent"
          />
        </motion.div>
      </div>

      {/* Floating Product Preview */}
      <motion.div
        initial={{ opacity: 0, x: 100 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 1.2, delay: 1.8, ease: 'power2.out' }}
        className="absolute right-8 top-1/2 transform -translate-y-1/2 hidden xl:block"
      >
        <motion.div
          animate={{ y: [-10, 10, -10] }}
          transition={{ duration: 4, repeat: Infinity, ease: 'easeInOut' }}
          className="w-64 h-80 rounded-3xl overflow-hidden shadow-luxury-lg"
        >
          <img
            src={luxuryImages.hero.main}
            alt="Luxury Fashion"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
        </motion.div>
      </motion.div>
    </section>
  );
};

export default Hero;
