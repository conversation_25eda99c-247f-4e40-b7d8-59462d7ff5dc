import React, { useEffect, useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Crown } from 'lucide-react';
import { useStaggerAnimation, useRevealAnimation } from '../hooks/useParallax';
import { featuredCollections } from '../data/luxuryImages';

interface Collection {
  id: number;
  title: string;
  subtitle: string;
  description: string;
  price: string;
  image: string;
  featured: boolean;
}

const FeaturedCollections: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const sectionRef = useRef<HTMLElement>(null);
  const headerRef = useRevealAnimation(0.2);
  const gridRef = useStaggerAnimation(0.15);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section
      ref={sectionRef}
      id="collections"
      className="py-32 bg-luxury-gradient relative overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute inset-0">
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.1, 0.2, 0.1]
          }}
          transition={{ duration: 8, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-20 right-20 w-64 h-64 bg-luxury-200/20 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.1, 1, 1.1],
            opacity: [0.05, 0.15, 0.05]
          }}
          transition={{ duration: 10, repeat: Infinity, ease: 'easeInOut', delay: 2 }}
          className="absolute bottom-20 left-20 w-48 h-48 bg-rosegold-300/15 rounded-full blur-3xl"
        />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">

        {/* Section Header */}
        <div ref={headerRef} className="text-center mb-20">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="inline-flex items-center px-6 py-3 mb-8 glass-effect rounded-full border border-luxury-200/50 shadow-luxury"
          >
            <Crown className="w-5 h-5 text-luxury-600 mr-3" />
            <span className="text-sm font-semibold text-gray-800 tracking-widest">
              FEATURED COLLECTIONS
            </span>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.4 }}
            className="text-5xl sm:text-6xl lg:text-7xl font-bold text-gray-900 mb-8"
          >
            <span className="block font-serif leading-tight">Curated</span>
            <span className="block luxury-text-gradient font-script leading-tight">
              Excellence
            </span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.6 }}
            className="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed font-luxury"
          >
            Each piece in our collection tells a story of craftsmanship,
            elegance, and timeless beauty designed for the modern woman.
          </motion.p>
        </div>

        {/* Collections Grid */}
        <div ref={gridRef} className="grid grid-cols-1 lg:grid-cols-3 gap-10">
          {featuredCollections.map((collection, index) => (
            <motion.div
              key={collection.id}
              onHoverStart={() => setHoveredCard(collection.id)}
              onHoverEnd={() => setHoveredCard(null)}
              className="group cursor-pointer"
            >
              <div className="relative overflow-hidden rounded-3xl aspect-[3/4] mb-8 shadow-luxury-lg">

                {/* Featured Badge */}
                {collection.featured && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="absolute top-6 left-6 z-20"
                  >
                    <div className="px-4 py-2 glass-effect rounded-full border border-luxury-300/50 shadow-luxury">
                      <span className="text-xs font-bold text-luxury-700 tracking-wide flex items-center">
                        <Star className="w-3 h-3 mr-1 fill-current" />
                        FEATURED
                      </span>
                    </div>
                  </motion.div>
                )}

                {/* Collection Image */}
                <div className="absolute inset-0">
                  <img
                    src={collection.image}
                    alt={collection.title}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
                </div>

                {/* Hover Overlay */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: hoveredCard === collection.id ? 1 : 0 }}
                  transition={{ duration: 0.3 }}
                  className="absolute inset-0 bg-luxury-900/20 backdrop-blur-sm"
                >
                  <div className="absolute bottom-6 left-6 right-6">
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="w-full px-6 py-4 glass-effect text-white rounded-full font-semibold hover:bg-white/20 transition-all duration-300 flex items-center justify-center shadow-luxury"
                    >
                      View Collection
                      <ArrowRight className="ml-3 w-5 h-5" />
                    </motion.button>
                  </div>
                </motion.div>

                {/* Floating Elements */}
                <motion.div
                  animate={{
                    y: hoveredCard === collection.id ? [-5, 5, -5] : 0,
                    opacity: hoveredCard === collection.id ? [0.6, 1, 0.6] : 0
                  }}
                  transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
                  className="absolute top-1/4 right-6"
                >
                  <Sparkles className="w-6 h-6 text-white/80" />
                </motion.div>
              </div>

              {/* Collection Info */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 + 0.3 }}
                className="space-y-4"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-luxury-600 font-semibold tracking-widest uppercase">
                      {collection.subtitle}
                    </p>
                    <h3 className="text-2xl font-bold text-gray-900 font-serif">
                      {collection.title}
                    </h3>
                  </div>
                  <motion.p
                    whileHover={{ scale: 1.05 }}
                    className="text-xl font-bold text-gray-900 px-4 py-2 bg-champagne-100 rounded-full"
                  >
                    {collection.price}
                  </motion.p>
                </div>

                <p className="text-gray-700 leading-relaxed font-luxury">
                  {collection.description}
                </p>

                <motion.button
                  whileHover={{ x: 5 }}
                  className="group/btn text-luxury-600 font-semibold flex items-center hover:text-luxury-700 transition-colors duration-300"
                >
                  Discover More
                  <ArrowRight className="ml-2 w-4 h-4 group-hover/btn:translate-x-1 transition-transform duration-300" />
                </motion.button>
              </motion.div>
            </motion.div>
          ))}
        </div>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.8 }}
          className="text-center mt-20"
        >
          <motion.button
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.98 }}
            className="luxury-button shadow-luxury-lg"
          >
            View All Collections
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default FeaturedCollections;
