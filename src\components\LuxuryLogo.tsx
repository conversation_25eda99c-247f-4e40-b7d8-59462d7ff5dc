import React from 'react';
import { motion } from 'framer-motion';
import { Crown, Sparkles } from 'lucide-react';

interface LuxuryLogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'light' | 'dark' | 'gradient';
  className?: string;
}

const LuxuryLogo: React.FC<LuxuryLogoProps> = ({ 
  size = 'md', 
  variant = 'light',
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'h-8',
    md: 'h-12',
    lg: 'h-16',
    xl: 'h-20'
  };

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-2xl',
    lg: 'text-3xl',
    xl: 'text-4xl'
  };

  const iconSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-10 h-10'
  };

  const getTextColor = () => {
    switch (variant) {
      case 'dark':
        return 'text-gray-900';
      case 'gradient':
        return 'luxury-text-gradient';
      default:
        return 'text-white';
    }
  };

  const getIconColor = () => {
    switch (variant) {
      case 'dark':
        return 'text-luxury-600';
      case 'gradient':
        return 'text-luxury-500';
      default:
        return 'text-white';
    }
  };

  return (
    <motion.div 
      className={`flex items-center space-x-3 ${className}`}
      whileHover={{ scale: 1.05 }}
      transition={{ duration: 0.2 }}
    >
      {/* Logo Icon */}
      <div className="relative">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: 'linear' }}
          className="absolute inset-0 opacity-20"
        >
          <div className={`${sizeClasses[size]} aspect-square border-2 border-dashed ${getIconColor()} rounded-full`} />
        </motion.div>
        
        <div className={`relative ${sizeClasses[size]} aspect-square bg-gradient-to-br from-luxury-500 to-luxury-700 rounded-full flex items-center justify-center shadow-luxury`}>
          <Crown className={`${iconSizeClasses[size]} text-white`} />
          
          {/* Floating sparkles */}
          <motion.div
            animate={{ 
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
            className="absolute -top-1 -right-1"
          >
            <Sparkles className="w-3 h-3 text-champagne-300" />
          </motion.div>
        </div>
      </div>

      {/* Brand Text */}
      <div className="flex flex-col">
        <motion.h1 
          className={`${textSizeClasses[size]} font-serif font-bold leading-tight ${getTextColor()}`}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          Dollz
        </motion.h1>
        <motion.p 
          className={`text-xs font-medium tracking-widest uppercase ${getIconColor()} opacity-90`}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          Boutique
        </motion.p>
      </div>
    </motion.div>
  );
};

export default LuxuryLogo;
